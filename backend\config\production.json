{"app": {"name": "Enterprise Backend API", "version": "1.0.0", "environment": "production"}, "server": {"port": 3001, "host": "0.0.0.0", "timeout": 30000}, "database": {"url": "${DATABASE_URL}", "pool": {"min": 2, "max": 10, "idle": 10000, "acquire": 60000}, "logging": false, "ssl": {"require": false, "rejectUnauthorized": false}}, "redis": {"url": "${REDIS_URL}", "retryDelayOnFailover": 100, "maxRetriesPerRequest": 3, "lazyConnect": true}, "kafka": {"brokers": ["${KAFKA_BROKERS}"], "clientId": "backend-api", "groupId": "backend-api-group", "retry": {"retries": 5, "initialRetryTime": 300}}, "consul": {"host": "${CONSUL_HOST}", "port": "${CONSUL_PORT}", "secure": false}, "tracing": {"enabled": true, "jaegerEndpoint": "${JAEGER_ENDPOINT}", "serviceName": "backend-api", "sampleRate": 1.0}, "metrics": {"enabled": true, "port": "${METRICS_PORT}", "path": "/metrics"}, "logging": {"level": "${LOG_LEVEL}", "format": "json", "timestamp": true, "correlationId": true}, "security": {"jwt": {"secret": "${JWT_SECRET}", "expiresIn": "24h", "algorithm": "HS256"}, "rateLimiting": {"enabled": true, "windowMs": 60000, "max": 1000}, "cors": {"enabled": true, "origin": "*", "credentials": true}, "helmet": {"enabled": true}}, "circuitBreaker": {"enabled": true, "failureThreshold": 5, "resetTimeout": 60000, "timeout": 30000}, "llmService": {"url": "${LLM_SERVICE_URL}", "timeout": 120000, "retries": 3}}