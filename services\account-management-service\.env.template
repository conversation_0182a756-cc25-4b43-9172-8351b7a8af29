# Account Management Service Environment Template
# Copy this file to .env.local and fill in the actual values

# Application Configuration
NODE_ENV=development
PORT=3012
HOST=localhost

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/x_marketing_platform
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_KEY_PREFIX=account-mgmt:

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=account-management-service
KAFKA_GROUP_ID=account-management-group
DISABLE_KAFKA=false

# Consul Configuration
CONSUL_HOST=localhost
CONSUL_PORT=8500
CONSUL_SERVICE_NAME=account-management-service
DISABLE_CONSUL=false

# Twitter API Configuration (REQUIRED for OAuth)
TWITTER_CONSUMER_KEY=your-twitter-consumer-key
TWITTER_CONSUMER_SECRET=your-twitter-consumer-secret
TWITTER_CALLBACK_URL=http://localhost:3012/oauth/callback

# JWT Configuration (REQUIRED - Generate secure keys)
JWT_SECRET=your-super-secret-jwt-key-that-should-be-at-least-32-characters-long
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key-that-should-be-at-least-32-characters-long

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
ACCOUNT_RATE_LIMIT_MAX=20

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9092
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Service Discovery
SERVICE_VERSION=1.0.0
SERVICE_TAGS=account-management,oauth,twitter,microservice

# Health Checks
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Enterprise Features
ENTERPRISE_MODE=true
ENABLE_AUDIT_LOGGING=true
ENABLE_SECURITY_MONITORING=true

# Account Management Specific
MAX_ACCOUNTS_PER_USER=5
ACCOUNT_HEALTH_CHECK_INTERVAL=300000
OAUTH_SESSION_TIMEOUT=600000

# External Services
USER_MANAGEMENT_SERVICE_URL=http://localhost:3011
BACKEND_SERVICE_URL=http://localhost:3001
LLM_SERVICE_URL=http://localhost:3003
TELEGRAM_BOT_URL=http://localhost:3002

# Proxy Configuration (Optional)
PROXY_ENABLED=false
PROXY_ROTATION_ENABLED=false
PROXY_POOL_SIZE=10

# Fingerprinting (Optional)
FINGERPRINTING_ENABLED=false
USER_AGENT_ROTATION=true
VIEWPORT_RANDOMIZATION=true

# Account Safety
ACCOUNT_SAFETY_CHECKS=true
SUSPENSION_RISK_THRESHOLD=0.7
AUTO_PAUSE_ON_RISK=true
RATE_LIMIT_BUFFER=0.8

# Compliance
COMPLIANCE_MONITORING=true
CONTENT_FILTERING=true
SPAM_DETECTION=true
AUTOMATED_REPORTING=true
