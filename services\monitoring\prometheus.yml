global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # User Management Service
  - job_name: 'user-management-service'
    static_configs:
      - targets: ['user-management-service:9091']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Account Management Service
  - job_name: 'account-management-service'
    static_configs:
      - targets: ['account-management-service:9092']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Campaign Management Service
  - job_name: 'campaign-management-service'
    static_configs:
      - targets: ['campaign-management-service:9093']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # PostgreSQL Exporter
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Kafka Exporter
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka-exporter:9308']
    scrape_interval: 30s

  # Node Exporter (system metrics)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Consul
  - job_name: 'consul'
    static_configs:
      - targets: ['consul:8500']
    metrics_path: '/v1/agent/metrics'
    params:
      format: ['prometheus']
    scrape_interval: 30s
