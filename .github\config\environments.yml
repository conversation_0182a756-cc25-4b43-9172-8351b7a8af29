# Multi-Environment Configuration Management
# Phase 3: Advanced CI/CD Features - Deployment Modernization

# Global environment settings
global:
  version: "3.0"
  config_format: "yaml"
  encryption_enabled: true
  secret_management: "github_secrets"
  
# Environment definitions
environments:
  development:
    name: "Development"
    description: "Development environment for testing and debugging"
    tier: "dev"
    stability: "unstable"
    
    # Infrastructure configuration
    infrastructure:
      replicas: 1
      cpu_limit: "500m"
      memory_limit: "512Mi"
      storage_size: "1Gi"
      auto_scaling: false
      
    # Network configuration
    network:
      ingress_enabled: true
      ssl_enabled: false
      load_balancer: false
      cdn_enabled: false
      
    # Database configuration
    database:
      type: "postgresql"
      size: "small"
      backup_enabled: false
      high_availability: false
      connection_pool_size: 5
      
    # Cache configuration
    cache:
      type: "redis"
      size: "small"
      persistence: false
      cluster_mode: false
      
    # Application configuration
    application:
      log_level: "debug"
      debug_mode: true
      rate_limiting: false
      session_timeout: 7200
      cache_ttl: 300
      
    # Twikit configuration
    twikit:
      rate_limit_strict: false
      session_encryption: false
      proxy_rotation: false
      anti_detection_level: "basic"
      session_persistence: true
      
    # Monitoring configuration
    monitoring:
      enabled: false
      metrics_collection: false
      alerting: false
      log_retention: "7d"
      
    # Security configuration
    security:
      authentication_required: false
      authorization_enabled: false
      audit_logging: false
      vulnerability_scanning: true
      
  staging:
    name: "Staging"
    description: "Staging environment for pre-production testing"
    tier: "staging"
    stability: "stable"
    
    # Infrastructure configuration
    infrastructure:
      replicas: 2
      cpu_limit: "1000m"
      memory_limit: "1Gi"
      storage_size: "5Gi"
      auto_scaling: true
      
    # Network configuration
    network:
      ingress_enabled: true
      ssl_enabled: true
      load_balancer: true
      cdn_enabled: false
      
    # Database configuration
    database:
      type: "postgresql"
      size: "medium"
      backup_enabled: true
      high_availability: false
      connection_pool_size: 10
      
    # Cache configuration
    cache:
      type: "redis"
      size: "medium"
      persistence: true
      cluster_mode: false
      
    # Application configuration
    application:
      log_level: "info"
      debug_mode: false
      rate_limiting: true
      session_timeout: 3600
      cache_ttl: 1800
      
    # Twikit configuration
    twikit:
      rate_limit_strict: true
      session_encryption: true
      proxy_rotation: false
      anti_detection_level: "enhanced"
      session_persistence: true
      
    # Monitoring configuration
    monitoring:
      enabled: true
      metrics_collection: true
      alerting: true
      log_retention: "30d"
      
    # Security configuration
    security:
      authentication_required: true
      authorization_enabled: true
      audit_logging: true
      vulnerability_scanning: true
      
  production:
    name: "Production"
    description: "Production environment for live traffic"
    tier: "prod"
    stability: "highly_stable"
    
    # Infrastructure configuration
    infrastructure:
      replicas: 3
      cpu_limit: "2000m"
      memory_limit: "2Gi"
      storage_size: "20Gi"
      auto_scaling: true
      
    # Network configuration
    network:
      ingress_enabled: true
      ssl_enabled: true
      load_balancer: true
      cdn_enabled: true
      
    # Database configuration
    database:
      type: "postgresql"
      size: "large"
      backup_enabled: true
      high_availability: true
      connection_pool_size: 20
      
    # Cache configuration
    cache:
      type: "redis"
      size: "large"
      persistence: true
      cluster_mode: true
      
    # Application configuration
    application:
      log_level: "warn"
      debug_mode: false
      rate_limiting: true
      session_timeout: 1800
      cache_ttl: 3600
      
    # Twikit configuration
    twikit:
      rate_limit_strict: true
      session_encryption: true
      proxy_rotation: true
      anti_detection_level: "maximum"
      session_persistence: true
      
    # Monitoring configuration
    monitoring:
      enabled: true
      metrics_collection: true
      alerting: true
      log_retention: "90d"
      
    # Security configuration
    security:
      authentication_required: true
      authorization_enabled: true
      audit_logging: true
      vulnerability_scanning: true

# Service-specific environment configurations
services:
  backend:
    development:
      port: 3001
      workers: 1
      max_connections: 100
      timeout: 30000
      
    staging:
      port: 3001
      workers: 2
      max_connections: 500
      timeout: 15000
      
    production:
      port: 3001
      workers: 4
      max_connections: 1000
      timeout: 10000
      
  frontend:
    development:
      port: 3000
      build_optimization: false
      ssr_enabled: false
      static_generation: false
      
    staging:
      port: 3000
      build_optimization: true
      ssr_enabled: true
      static_generation: true
      
    production:
      port: 3000
      build_optimization: true
      ssr_enabled: true
      static_generation: true
      
  telegram-bot:
    development:
      port: 3002
      webhook_enabled: false
      polling_interval: 1000
      session_cleanup: false
      
    staging:
      port: 3002
      webhook_enabled: true
      polling_interval: 500
      session_cleanup: true
      
    production:
      port: 3002
      webhook_enabled: true
      polling_interval: 100
      session_cleanup: true
      
  llm-service:
    development:
      port: 3003
      model_caching: false
      batch_processing: false
      gpu_enabled: false
      
    staging:
      port: 3003
      model_caching: true
      batch_processing: true
      gpu_enabled: false
      
    production:
      port: 3003
      model_caching: true
      batch_processing: true
      gpu_enabled: true

# Deployment strategies per environment
deployment_strategies:
  development:
    strategy: "recreate"
    rollout_timeout: "5m"
    health_check_timeout: "2m"
    rollback_enabled: false
    
  staging:
    strategy: "rolling"
    rollout_timeout: "10m"
    health_check_timeout: "5m"
    rollback_enabled: true
    max_surge: 1
    max_unavailable: 0
    
  production:
    strategy: "blue-green"
    rollout_timeout: "15m"
    health_check_timeout: "10m"
    rollback_enabled: true
    traffic_split: "10-90"  # 10% new, 90% old initially
    validation_period: "5m"

# Environment promotion rules
promotion_rules:
  development_to_staging:
    required_checks:
      - "unit_tests_passed"
      - "integration_tests_passed"
      - "security_scan_passed"
      - "performance_baseline_met"
    approval_required: false
    automatic: true
    
  staging_to_production:
    required_checks:
      - "staging_validation_passed"
      - "load_testing_passed"
      - "security_audit_passed"
      - "twikit_compliance_verified"
    approval_required: true
    automatic: false
    approvers:
      - "team:platform-engineers"
      - "team:security-team"

# Secret management configuration
secrets:
  development:
    database_url: "DEV_DATABASE_URL"
    redis_url: "DEV_REDIS_URL"
    jwt_secret: "DEV_JWT_SECRET"
    twikit_credentials: "DEV_TWIKIT_CREDENTIALS"
    
  staging:
    database_url: "STAGING_DATABASE_URL"
    redis_url: "STAGING_REDIS_URL"
    jwt_secret: "STAGING_JWT_SECRET"
    twikit_credentials: "STAGING_TWIKIT_CREDENTIALS"
    
  production:
    database_url: "PROD_DATABASE_URL"
    redis_url: "PROD_REDIS_URL"
    jwt_secret: "PROD_JWT_SECRET"
    twikit_credentials: "PROD_TWIKIT_CREDENTIALS"

# Health check configurations
health_checks:
  endpoints:
    health: "/health"
    ready: "/ready"
    metrics: "/metrics"
    
  timeouts:
    development: 30
    staging: 15
    production: 10
    
  intervals:
    development: 60
    staging: 30
    production: 10
    
  retries:
    development: 3
    staging: 5
    production: 10
