# X Marketing Platform - Local Development Environment Configuration
# Copy this file to .env.local and update with your actual values

# =============================================================================
# CORE ENVIRONMENT
# =============================================================================
NODE_ENV=development
LOG_LEVEL=debug
ENABLE_REQUEST_LOGGING=true

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=postgresql://postgres:password@localhost:5432/x_marketing_automation
REDIS_URL=redis://localhost:6379

# =============================================================================
# SERVICE URLS
# =============================================================================
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001
LLM_SERVICE_URL=http://localhost:3003
TELEGRAM_BOT_URL=http://localhost:3002

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
JWT_SECRET=local-development-jwt-secret-key-32-chars-long-please-change-in-production
BCRYPT_ROUNDS=10

# =============================================================================
# X (TWITTER) API CONFIGURATION
# =============================================================================
# Get these from https://developer.twitter.com/
X_API_KEY=your-x-api-key-here
X_API_SECRET=your-x-api-secret-here
X_BEARER_TOKEN=your-x-bearer-token-here
X_ACCESS_TOKEN=your-x-access-token-here
X_ACCESS_TOKEN_SECRET=your-x-access-token-secret-here

# =============================================================================
# AI/LLM SERVICE CONFIGURATION
# =============================================================================
# Get from https://huggingface.co/settings/tokens
HUGGINGFACE_API_KEY=your-huggingface-api-key-here

# Optional: OpenAI API Key (for enhanced content generation)
OPENAI_API_KEY=your-openai-api-key-here

# Optional: Anthropic API Key (for Claude integration)
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# =============================================================================
# TELEGRAM BOT CONFIGURATION
# =============================================================================
# Get from @BotFather on Telegram
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here
WEBHOOK_URL=http://localhost:3002/webhook
ENABLE_POLLING=true

# =============================================================================
# RATE LIMITING & SECURITY
# =============================================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
MAX_REQUEST_SIZE=10mb
ENABLE_CORS=true
TRUST_PROXY=false

# =============================================================================
# AUTOMATION CONFIGURATION
# =============================================================================
AUTOMATION_MODE=true
ENABLE_ANALYTICS=true
CACHE_TTL=3600

# Automation Safety Limits
MAX_LIKES_PER_HOUR=30
MAX_FOLLOWS_PER_HOUR=20
MAX_COMMENTS_PER_HOUR=15
MAX_POSTS_PER_DAY=10

# Quality Thresholds
MIN_CONTENT_QUALITY_SCORE=0.7
MIN_ENGAGEMENT_QUALITY_SCORE=0.8
COMPLIANCE_THRESHOLD=0.9

# =============================================================================
# LLM SERVICE SPECIFIC
# =============================================================================
FLASK_ENV=development
FLASK_DEBUG=1
ENABLE_CACHING=true
MAX_TOKENS=2048
TEMPERATURE=0.7

# Model Configuration
DEFAULT_TEXT_MODEL=microsoft/DialoGPT-large
DEFAULT_CONTENT_MODEL=gpt2
DEFAULT_SENTIMENT_MODEL=cardiffnlp/twitter-roberta-base-sentiment-latest

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_TRACKING=true
ANALYTICS_RETENTION_DAYS=90

# =============================================================================
# DEVELOPMENT FEATURES
# =============================================================================
ENABLE_DEBUG_MODE=true
ENABLE_MOCK_SERVICES=false
SKIP_AUTH_IN_DEV=false
ENABLE_API_DOCS=true

# =============================================================================
# PROXY CONFIGURATION (Optional)
# =============================================================================
# HTTP_PROXY=http://your-proxy:8080
# HTTPS_PROXY=http://your-proxy:8080
# NO_PROXY=localhost,127.0.0.1

# =============================================================================
# CONTENT GENERATION SETTINGS
# =============================================================================
DEFAULT_CONTENT_TONE=professional
DEFAULT_CONTENT_LENGTH=medium
ENABLE_HASHTAG_GENERATION=true
ENABLE_IMAGE_GENERATION=false
ENABLE_VIDEO_GENERATION=false

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================
ENABLE_EMAIL_NOTIFICATIONS=false
ENABLE_TELEGRAM_NOTIFICATIONS=true
ENABLE_WEBHOOK_NOTIFICATIONS=false

# Email Configuration (if enabled)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# =============================================================================
# BACKUP & STORAGE
# =============================================================================
ENABLE_AUTO_BACKUP=false
BACKUP_INTERVAL_HOURS=24
MAX_BACKUP_FILES=7

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_MULTI_ACCOUNT=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_COMPETITOR_ANALYSIS=true
ENABLE_TREND_ANALYSIS=true
ENABLE_CONTENT_SCHEDULING=true
ENABLE_BULK_OPERATIONS=true
ENABLE_CAMPAIGN_AUTOMATION=true
ENABLE_NATURAL_LANGUAGE_CAMPAIGNS=true

# =============================================================================
# DEMO MODE (for testing without real API keys)
# =============================================================================
DEMO_MODE=true
USE_MOCK_X_API=true
USE_MOCK_LLM_API=false
USE_MOCK_TELEGRAM_API=false
