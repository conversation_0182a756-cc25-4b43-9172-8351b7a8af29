{"name": "x-marketing-platform", "version": "1.0.0", "description": "Comprehensive X (Twitter) marketing automation platform", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:telegram\" \"npm run dev:llm\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:telegram": "cd telegram-bot && npm run dev", "dev:llm": "cd llm-service && python app.py", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install && cd ../telegram-bot && npm install && cd ../llm-service && pip install -r requirements.txt", "db:migrate": "cd backend && npx prisma migrate dev", "db:generate": "cd backend && npx prisma generate", "db:seed": "cd backend && npx prisma db seed", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix"}, "keywords": ["twitter", "x", "automation", "marketing", "crypto", "social-media", "bot", "analytics"], "author": "X Marketing Platform Team", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "eslint": "^8.54.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"axios": "^1.10.0", "dotenv": "^17.2.0"}}