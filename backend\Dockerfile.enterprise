# Enterprise Backend Dockerfile
# Multi-stage build with security hardening and performance optimization

# Build stage
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl \
    openssl

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./
COPY prisma/ ./prisma/

# Install all dependencies (including dev dependencies for build)
RUN npm install --no-audit --no-fund && npm cache clean --force

# Generate Prisma client
RUN npx prisma generate

# Copy source code
COPY src/ ./src/

# Build TypeScript
RUN npm run build

# Production stage
FROM node:20-alpine AS production

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S backend -u 1001

# Install runtime dependencies and security updates
RUN apk add --no-cache \
    dumb-init \
    curl \
    ca-certificates \
    tzdata \
    openssl && \
    apk upgrade --no-cache

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=backend:nodejs /app/dist ./dist
COPY --from=builder --chown=backend:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=backend:nodejs /app/package*.json ./
COPY --from=builder --chown=backend:nodejs /app/prisma ./prisma

# Copy enterprise configuration files
COPY --chown=backend:nodejs config/ ./config/
COPY --chown=backend:nodejs scripts/ ./scripts/

# Create necessary directories
RUN mkdir -p /app/logs /app/tmp /app/uploads && \
    chown -R backend:nodejs /app/logs /app/tmp /app/uploads

# Set environment variables
ENV NODE_ENV=production \
    NODE_OPTIONS="--max-old-space-size=1024" \
    PORT=3001 \
    METRICS_PORT=9094 \
    LOG_LEVEL=info \
    ENABLE_METRICS=true \
    ENABLE_TRACING=true \
    ENABLE_HEALTH_CHECKS=true \
    DATABASE_POOL_SIZE=20 \
    DATABASE_CONNECTION_TIMEOUT=30000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3001/api/health || exit 1

# Switch to non-root user
USER backend

# Expose ports
EXPOSE 3001 9094

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application with database migration
CMD ["sh", "-c", "npx prisma migrate deploy && node dist/index.js"]
