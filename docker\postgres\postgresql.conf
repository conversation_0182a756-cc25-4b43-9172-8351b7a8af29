# PostgreSQL Configuration for Production
# Memory Configuration
shared_buffers = 512MB
effective_cache_size = 2GB
maintenance_work_mem = 128MB
work_mem = 16MB

# Connection Configuration
max_connections = 200
superuser_reserved_connections = 3

# Write Ahead Logging (WAL) Configuration
wal_buffers = 32MB
checkpoint_completion_target = 0.9
checkpoint_timeout = 10min
max_wal_size = 2GB
min_wal_size = 1GB

# Query Planner Configuration
random_page_cost = 1.1
effective_io_concurrency = 200
default_statistics_target = 100

# Logging Configuration
log_destination = 'stderr'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 10MB

# Performance Monitoring
shared_preload_libraries = 'pg_stat_statements'
track_activity_query_size = 2048
track_functions = all
track_io_timing = on

# Autovacuum Configuration
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.1
autovacuum_analyze_scale_factor = 0.05

# Lock Configuration
deadlock_timeout = 1s
lock_timeout = 30s

# Background Writer Configuration
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0

# Archive Configuration (for backup)
archive_mode = on
archive_command = 'test ! -f /var/lib/postgresql/archive/%f && cp %p /var/lib/postgresql/archive/%f'

# Replication Configuration (if needed)
max_wal_senders = 3
wal_level = replica
hot_standby = on

# Security Configuration
ssl = off
password_encryption = scram-sha-256

# Locale Configuration
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'

# Time Zone
timezone = 'UTC'

# Other Configuration
listen_addresses = '*'
port = 5432
max_prepared_transactions = 0
shared_memory_type = mmap
