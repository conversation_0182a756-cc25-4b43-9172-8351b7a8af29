# Enterprise LLM Service Dockerfile
# Multi-stage build with security hardening and performance optimization

# Build stage
FROM python:3.11-slim AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements-prod.txt ./

# Create virtual environment and install dependencies
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements-prod.txt

# Production stage
FROM python:3.11-slim AS production

# Create non-root user
RUN groupadd -g 1001 python && \
    useradd -r -u 1001 -g python llm-service

# Install runtime dependencies and security updates
RUN apt-get update && apt-get install -y \
    dumb-init \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy virtual environment from builder stage
COPY --from=builder --chown=llm-service:python /opt/venv /opt/venv

# Copy application code
COPY --chown=llm-service:python . .

# Copy enterprise configuration files
COPY --chown=llm-service:python config/ ./config/
COPY --chown=llm-service:python scripts/ ./scripts/

# Create necessary directories
RUN mkdir -p /app/logs /app/tmp /app/models && \
    chown -R llm-service:python /app/logs /app/tmp /app/models

# Set environment variables
ENV PATH="/opt/venv/bin:$PATH" \
    PYTHONPATH="/app" \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    FLASK_ENV=production \
    PORT=3003 \
    METRICS_PORT=9095 \
    LOG_LEVEL=info \
    ENABLE_METRICS=true \
    ENABLE_TRACING=true \
    ENABLE_HEALTH_CHECKS=true \
    WORKERS=4 \
    TIMEOUT=120 \
    MAX_REQUESTS=1000 \
    MAX_REQUESTS_JITTER=100

# Health check
HEALTHCHECK --interval=30s --timeout=15s --start-period=90s --retries=3 \
    CMD curl -f http://localhost:3003/health || exit 1

# Switch to non-root user
USER llm-service

# Expose ports
EXPOSE 3003 9095

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application with Uvicorn for FastAPI production
CMD ["uvicorn", "fastapi_app:app", "--host", "0.0.0.0", "--port", "3003", "--workers", "4", "--access-log", "--log-level", "info"]
