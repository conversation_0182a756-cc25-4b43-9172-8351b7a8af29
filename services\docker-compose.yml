version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: x-marketing-postgres
    environment:
      POSTGRES_DB: x_marketing_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - x-marketing-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: x-marketing-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - x-marketing-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka (with Zookeeper)
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: x-marketing-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - x-marketing-network

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: x-marketing-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - x-marketing-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Consul Service Discovery
  consul:
    image: consul:latest
    container_name: x-marketing-consul
    ports:
      - "8500:8500"
    environment:
      CONSUL_BIND_INTERFACE: eth0
    networks:
      - x-marketing-network
    healthcheck:
      test: ["CMD", "consul", "members"]
      interval: 30s
      timeout: 10s
      retries: 3

  # User Management Service
  user-management-service:
    build:
      context: ./user-management-service
      dockerfile: Dockerfile
    container_name: x-marketing-user-service
    ports:
      - "3011:3011"
    environment:
      NODE_ENV: production
      DATABASE_URL: ********************************************/x_marketing_platform
      REDIS_URL: redis://redis:6379
      KAFKA_BROKERS: kafka:9092
      CONSUL_HOST: consul
      JWT_SECRET: your-super-secret-jwt-key-that-should-be-at-least-32-characters-long
      JWT_REFRESH_SECRET: your-super-secret-refresh-jwt-key-that-should-be-at-least-32-characters-long
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
      consul:
        condition: service_healthy
    networks:
      - x-marketing-network
    restart: unless-stopped

  # Account Management Service
  account-management-service:
    build:
      context: ./account-management-service
      dockerfile: Dockerfile
    container_name: x-marketing-account-service
    ports:
      - "3012:3012"
    environment:
      NODE_ENV: production
      DATABASE_URL: ********************************************/x_marketing_platform
      REDIS_URL: redis://redis:6379
      KAFKA_BROKERS: kafka:9092
      CONSUL_HOST: consul
      JWT_SECRET: your-super-secret-jwt-key-that-should-be-at-least-32-characters-long
      JWT_REFRESH_SECRET: your-super-secret-refresh-jwt-key-that-should-be-at-least-32-characters-long
      TWITTER_CONSUMER_KEY: not-available-regional-restrictions
      TWITTER_CONSUMER_SECRET: not-available-regional-restrictions
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
      consul:
        condition: service_healthy
    networks:
      - x-marketing-network
    restart: unless-stopped

  # Campaign Management Service
  campaign-management-service:
    build:
      context: ./campaign-management-service
      dockerfile: Dockerfile
    container_name: x-marketing-campaign-service
    ports:
      - "3013:3013"
    environment:
      NODE_ENV: production
      DATABASE_URL: ********************************************/x_marketing_platform
      REDIS_URL: redis://redis:6379
      KAFKA_BROKERS: kafka:9092
      CONSUL_HOST: consul
      JWT_SECRET: your-super-secret-jwt-key-that-should-be-at-least-32-characters-long
      JWT_REFRESH_SECRET: your-super-secret-refresh-jwt-key-that-should-be-at-least-32-characters-long
      LLM_SERVICE_URL: http://localhost:3003
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
      consul:
        condition: service_healthy
    networks:
      - x-marketing-network
    restart: unless-stopped

  # Content Management Service
  content-management-service:
    build:
      context: ./content-management-service
      dockerfile: Dockerfile
    container_name: x-marketing-content-service
    ports:
      - "3014:3014"
    environment:
      NODE_ENV: production
      DATABASE_URL: ********************************************/x_marketing_platform
      REDIS_URL: redis://redis:6379
      KAFKA_BROKERS: kafka:9092
      CONSUL_HOST: consul
      JWT_SECRET: your-super-secret-jwt-key-that-should-be-at-least-32-characters-long
      JWT_REFRESH_SECRET: your-super-secret-refresh-jwt-key-that-should-be-at-least-32-characters-long
      LLM_SERVICE_URL: http://localhost:3003
      STORAGE_PROVIDER: local
      STORAGE_LOCAL_PATH: /app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
      consul:
        condition: service_healthy
    networks:
      - x-marketing-network
    volumes:
      - content_uploads:/app/uploads
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  content_uploads:

networks:
  x-marketing-network:
    driver: bridge
