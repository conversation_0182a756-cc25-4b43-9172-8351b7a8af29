# Redis Configuration for Production

# Network Configuration
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300

# General Configuration
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# Snapshotting Configuration
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Replication Configuration
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# Security Configuration
requirepass redis123
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command EVAL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_b840fc02d524045429941cc15f59e41cb7be6c52"

# Memory Management
maxmemory 1gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Lazy Freeing
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# Append Only File Configuration
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Lua Scripting
lua-time-limit 5000

# Slow Log Configuration
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency Monitoring
latency-monitor-threshold 100

# Event Notification
notify-keyspace-events ""

# Hash Configuration
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List Configuration
list-max-ziplist-size -2
list-compress-depth 0

# Set Configuration
set-max-intset-entries 512

# Sorted Set Configuration
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog Configuration
hll-sparse-max-bytes 3000

# Streams Configuration
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active Rehashing
activerehashing yes

# Client Output Buffer Limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client Query Buffer
client-query-buffer-limit 1gb

# Protocol Buffer
proto-max-bulk-len 512mb

# Frequency of rehashing
hz 10

# Dynamic HZ
dynamic-hz yes

# AOF Rewrite Incremental Fsync
aof-rewrite-incremental-fsync yes

# RDB Save Incremental Fsync
rdb-save-incremental-fsync yes

# LFU Configuration
lfu-log-factor 10
lfu-decay-time 1

# Active Defragmentation
activedefrag no
active-defrag-ignore-bytes 100mb
active-defrag-threshold-lower 10
active-defrag-threshold-upper 100
active-defrag-cycle-min 5
active-defrag-cycle-max 75
active-defrag-max-scan-fields 1000

# Jemalloc Background Thread
jemalloc-bg-thread yes

# EOF
