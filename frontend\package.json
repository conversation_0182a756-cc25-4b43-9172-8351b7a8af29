{"name": "x-marketing-frontend", "version": "1.0.0", "description": "Frontend dashboard for X Marketing Platform", "private": true, "scripts": {"dev": "next dev -p 3004", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-datepicker": "^4.19.4", "@types/react-dom": "^18.2.18", "@types/react-syntax-highlighter": "^15.5.11", "@types/react-table": "^7.7.18", "@types/react-window": "^1.8.8", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "^14.0.4", "postcss": "^8.4.32", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-datepicker": "^4.25.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "react-markdown": "^9.0.1", "react-query": "^3.39.3", "react-select": "^5.8.0", "react-syntax-highlighter": "^15.5.0", "react-table": "^7.8.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "recharts": "^2.8.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.0", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}