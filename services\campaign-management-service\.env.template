# Campaign Management Service Environment Template
# Copy this file to .env.local and fill in the actual values

# Application Configuration
NODE_ENV=development
PORT=3013
HOST=localhost

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/x_marketing_platform
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_KEY_PREFIX=campaign-mgmt:

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=campaign-management-service
KAFKA_GROUP_ID=campaign-management-group
DISABLE_KAFKA=false

# Consul Configuration
CONSUL_HOST=localhost
CONSUL_PORT=8500
CONSUL_SERVICE_NAME=campaign-management-service
DISABLE_CONSUL=false

# LLM Service Configuration
LLM_SERVICE_URL=http://localhost:3003
LLM_API_KEY=your-llm-api-key
LLM_MODEL=gpt-3.5-turbo

# JWT Configuration (REQUIRED - Generate secure keys)
JWT_SECRET=your-super-secret-jwt-key-that-should-be-at-least-32-characters-long
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key-that-should-be-at-least-32-characters-long

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CAMPAIGN_RATE_LIMIT_MAX=50

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9093
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Service Discovery
SERVICE_VERSION=1.0.0
SERVICE_TAGS=campaign-management,automation,scheduling,microservice

# Health Checks
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Enterprise Features
ENTERPRISE_MODE=true
ENABLE_AUDIT_LOGGING=true
ENABLE_SECURITY_MONITORING=true

# Campaign Management Specific
MAX_CAMPAIGNS_PER_USER=10
MAX_POSTS_PER_CAMPAIGN=1000
MAX_AUTOMATIONS_PER_CAMPAIGN=20
SCHEDULER_INTERVAL=60000
CONTENT_GENERATION_TIMEOUT=30000

# External Services
USER_MANAGEMENT_SERVICE_URL=http://localhost:3011
ACCOUNT_MANAGEMENT_SERVICE_URL=http://localhost:3012
BACKEND_SERVICE_URL=http://localhost:3001
TELEGRAM_BOT_URL=http://localhost:3002

# Automation Settings
MAX_CONCURRENT_AUTOMATIONS=100
AUTOMATION_RETRY_ATTEMPTS=3
AUTOMATION_RETRY_DELAY=300000

# Content Settings
MAX_CONTENT_LENGTH=280
MAX_MEDIA_SIZE=5242880
SUPPORTED_MEDIA_TYPES=image/jpeg,image/png,image/gif,video/mp4
CONTENT_MODERATION_ENABLED=true

# Safety Settings
RATE_LIMIT_SAFETY_MARGIN=0.8
SPAM_DETECTION_THRESHOLD=0.7

# Performance Settings
BATCH_SIZE=100
PARALLEL_PROCESSING_LIMIT=10
CACHE_TTL=3600

# Notification Settings
WEBHOOK_TIMEOUT=10000
EMAIL_NOTIFICATIONS_ENABLED=false
SLACK_WEBHOOK_URL=

# Analytics Settings
ANALYTICS_RETENTION_DAYS=90
METRICS_AGGREGATION_INTERVAL=3600000
REAL_TIME_ANALYTICS_ENABLED=true

# AI/ML Settings
AI_CONTENT_GENERATION=true
AI_SENTIMENT_ANALYSIS=true
AI_HASHTAG_SUGGESTIONS=true
AI_OPTIMAL_TIMING=true
AI_AUDIENCE_TARGETING=true

# Scheduling Settings
TIMEZONE_SUPPORT=true
WORKING_HOURS_ENFORCEMENT=true
HOLIDAY_CALENDAR_INTEGRATION=false
SMART_SCHEDULING=true

# Budget Management
BUDGET_TRACKING=true
COST_OPTIMIZATION=true
SPEND_ALERTS=true
ROI_TRACKING=true
