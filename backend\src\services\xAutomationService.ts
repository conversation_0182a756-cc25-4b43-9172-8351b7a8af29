/**
 * Enterprise X (Twitter) Automation Service with Comprehensive Twikit Integration
 *
 * Provides enterprise-grade automation capabilities using Twikit as the underlying API client,
 * with advanced session management, rate limiting coordination, anti-detection measures,
 * and comprehensive error handling for production-scale social media automation.
 *
 * Features:
 * - Complete Twikit API integration (50+ methods)
 * - Enterprise session management with TwikitSessionManager
 * - Distributed rate limiting with GlobalRateLimitCoordinator
 * - Advanced anti-detection and proxy rotation
 * - Comprehensive error handling and retry mechanisms
 * - Quality checks and compliance validation
 * - Real-time monitoring and analytics
 * - Multi-account coordination and management
 *
 * @version 2.0.0 - Enterprise Twikit Integration
 * <AUTHOR> Automation Team
 */

import {
  logger,
  logTwikitAction,
  logTwikitSession,
  logTwikitPerformance,
  logRateLimit,
  logCircuitBreaker,
  logAuditTrail,
  generateCorrelationId,
  sanitizeData
} from '../utils/logger';
import { prisma } from '../lib/prisma';
import { cacheManager } from '../lib/cache';
import { TwikitSessionManager, TwikitSession, TwikitSessionOptions } from './twikitSessionManager';
import {
  GlobalRateLimitCoordinator,
  RateLimitAction,
  RateLimitPriority,
  RateLimitCheckRequest,
  RateLimitCheckResponse,
  AccountType
} from './globalRateLimitCoordinator';
import { EnterpriseAntiDetectionCoordinator } from './antiDetection/antiDetectionCoordinator';
import { ProxyRotationManager, ActionRiskLevel } from './proxyRotationManager';
import { TwikitConfigManager } from '../config/twikit';
import { IntelligentRetryEngine } from './intelligentRetryEngine';
import { enterpriseConfig, XAutomationConfig } from '../config/enterpriseConfig';
import { TwikitError, TwikitErrorType } from '../errors/enterpriseErrorFramework';



/**
 * Twikit Action Types - Complete enumeration of all available actions
 */
export enum TwikitActionType {
  // Authentication
  AUTHENTICATE = 'authenticate',
  LOGOUT = 'logout',

  // Tweet Operations
  CREATE_TWEET = 'create_tweet',
  DELETE_TWEET = 'delete_tweet',
  GET_TWEET = 'get_tweet_by_id',
  GET_TWEETS = 'get_tweets_by_ids',
  SEARCH_TWEETS = 'search_tweet',
  GET_USER_TWEETS = 'get_user_tweets',
  GET_TIMELINE = 'get_timeline',
  GET_LATEST_TIMELINE = 'get_latest_timeline',

  // Engagement Actions
  LIKE_TWEET = 'favorite_tweet',
  UNLIKE_TWEET = 'unfavorite_tweet',
  RETWEET = 'retweet',
  UNRETWEET = 'delete_retweet',
  BOOKMARK_TWEET = 'bookmark_tweet',
  UNBOOKMARK_TWEET = 'delete_bookmark',
  GET_BOOKMARKS = 'get_bookmarks',

  // User Operations
  FOLLOW_USER = 'follow_user',
  UNFOLLOW_USER = 'unfollow_user',
  BLOCK_USER = 'block_user',
  UNBLOCK_USER = 'unblock_user',
  MUTE_USER = 'mute_user',
  UNMUTE_USER = 'unmute_user',
  SEARCH_USER = 'search_user',
  GET_USER_BY_SCREEN_NAME = 'get_user_by_screen_name',
  GET_USER_BY_ID = 'get_user_by_id',
  GET_USER_FOLLOWERS = 'get_user_followers',
  GET_USER_FOLLOWING = 'get_user_following',

  // Direct Messages
  SEND_DM = 'send_dm',
  SEND_GROUP_DM = 'send_dm_to_group',
  DELETE_DM = 'delete_dm',
  GET_DM_HISTORY = 'get_dm_history',
  ADD_DM_REACTION = 'add_reaction_to_message',
  REMOVE_DM_REACTION = 'remove_reaction_from_message',

  // Media Operations
  UPLOAD_MEDIA = 'upload_media',
  CHECK_MEDIA_STATUS = 'check_media_status',
  CREATE_MEDIA_METADATA = 'create_media_metadata',

  // Polls
  CREATE_POLL = 'create_poll',
  VOTE_POLL = 'vote',

  // Lists
  CREATE_LIST = 'create_list',
  EDIT_LIST = 'edit_list',
  DELETE_LIST = 'delete_list',
  ADD_LIST_MEMBER = 'add_list_member',
  REMOVE_LIST_MEMBER = 'remove_list_member',
  GET_LIST_TWEETS = 'get_list_tweets',
  GET_LIST_MEMBERS = 'get_list_members',
  GET_LISTS = 'get_lists',

  // Communities
  SEARCH_COMMUNITY = 'search_community',
  JOIN_COMMUNITY = 'join_community',
  LEAVE_COMMUNITY = 'leave_community',
  GET_COMMUNITY_TWEETS = 'get_community_tweets',
  GET_COMMUNITY_MEMBERS = 'get_community_members',

  // Trends and Discovery
  GET_TRENDS = 'get_trends',
  GET_PLACE_TRENDS = 'get_place_trends',

  // Notifications
  GET_NOTIFICATIONS = 'get_notifications',

  // Health and Monitoring
  CHECK_HEALTH = 'check_health',
  GET_SESSION_METRICS = 'get_session_metrics'
}

/**
 * Enhanced Type Definitions for Enterprise Automation
 */
interface ContentData {
  text: string;
  mediaUrls?: string[];
  mediaIds?: string[];
  hashtags?: string[];
  mentions?: string[];
  scheduledFor?: Date;
  replyToTweetId?: string;
  quoteTweetId?: string;
  poll?: {
    choices: string[];
    durationMinutes: number;
  };
}

interface PostOptions {
  skipQualityCheck?: boolean;
  skipRateLimit?: boolean;
  priority?: RateLimitPriority;
  accountId?: string;
  sessionId?: string;
  enableAntiDetection?: boolean;
  customDelay?: number;
  retryOnFailure?: boolean;
}

interface QualityCheckResult {
  approved: boolean;
  score: number;
  reason?: string;
  suggestions?: string[];
  complianceFlags?: string[];
  sentimentScore?: number;
  spamProbability?: number;
}

interface RateLimitResult {
  allowed: boolean;
  reason?: string;
  retryAfter?: number;
}

interface AutomationStatus {
  isActive: boolean;
  activeFeatures: string[];
  lastActivity: Date;
  stats: {
    postsToday: number;
    likesToday: number;
    commentsToday: number;
    followsToday: number;
    retweetsToday: number;
    dmsToday: number;
    bookmarksToday: number;
  };
  health?: {
    sessionHealth: number;
    rateLimitStatus: string;
    proxyStatus: string;
    antiDetectionStatus: string;
  };
}

interface TwikitActionRequest {
  action: TwikitActionType;
  accountId: string;
  params: Record<string, any>;
  options?: PostOptions;
  metadata?: {
    campaignId?: string;
    userId?: string;
    priority?: RateLimitPriority;
    tags?: string[];
  };
}

interface TwikitActionResponse {
  success: boolean;
  data?: any;
  error?: string;
  errorType?: TwikitErrorType;
  metadata?: {
    sessionId?: string;
    rateLimitRemaining?: number;
    retryAfter?: number;
    executionTime?: number;
    proxyUsed?: string;
  };
}

/**
 * Enterprise X Automation Service with Comprehensive Twikit Integration
 *
 * This service provides enterprise-grade automation capabilities using Twikit as the underlying
 * API client, with advanced session management, rate limiting coordination, anti-detection
 * measures, and comprehensive error handling for production-scale social media automation.
 *
 * Key Features:
 * - Complete Twikit API integration (50+ methods)
 * - Enterprise session management with TwikitSessionManager
 * - Distributed rate limiting with GlobalRateLimitCoordinator
 * - Advanced anti-detection and proxy rotation
 * - Comprehensive error handling and retry mechanisms
 * - Quality checks and compliance validation
 * - Real-time monitoring and analytics
 * - Multi-account coordination and management
 */
export class XAutomationService {
  private sessionManager: TwikitSessionManager;
  private rateLimitCoordinator: GlobalRateLimitCoordinator;
  private antiDetectionCoordinator: EnterpriseAntiDetectionCoordinator;
  private proxyManager: ProxyRotationManager;
  private configManager: TwikitConfigManager;
  private retryEngine: IntelligentRetryEngine;
  private activeAutomations: Map<string, any>;
  private config: XAutomationConfig;
  private circuitBreakers: Map<string, { failures: number; lastFailure: Date; isOpen: boolean }>;
  private performanceMetrics: Map<string, { totalRequests: number; successRate: number; avgResponseTime: number }>;

  constructor(config?: Partial<XAutomationConfig>) {
    // Load enterprise configuration from centralized config manager
    const enterpriseXConfig = enterpriseConfig.getXAutomationConfig();

    // Initialize configuration with enterprise defaults and overrides
    this.config = enterpriseXConfig;


    // Initialize enterprise services
    this.sessionManager = new TwikitSessionManager();
    this.rateLimitCoordinator = new GlobalRateLimitCoordinator();
    this.antiDetectionCoordinator = new EnterpriseAntiDetectionCoordinator();
    this.retryEngine = IntelligentRetryEngine.getInstance();
    this.proxyManager = new ProxyRotationManager();
    this.configManager = TwikitConfigManager.getInstance();

    // Initialize tracking maps
    this.activeAutomations = new Map();
    this.circuitBreakers = new Map();
    this.performanceMetrics = new Map();

    logger.info('Enterprise X Automation Service initialized with Twikit integration', {
      config: this.config,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Initialize Twikit session for an account with enterprise features
   * Replaces Twitter API v2 client initialization with Twikit session management
   */
  async initializeTwikitSession(accountId: string, options?: TwikitSessionOptions): Promise<TwikitSession> {
    try {
      logger.info(`Initializing Twikit session for account ${accountId}`, { accountId, options });

      // Get account credentials from database
      const account = await prisma.xAccount.findUnique({
        where: { id: accountId },
        include: { user: true }
      });

      if (!account || !account.username || !account.password) {
        throw new TwikitError(
          TwikitErrorType.AUTHENTICATION_ERROR,
          'Account credentials not found or incomplete',
          { accountId }
        );
      }

      // Create session with enterprise options
      const sessionOptions: TwikitSessionOptions = {
        accountId,
        credentials: {
          username: account.username || '',
          email: account.email || '',
          password: account.password || ''
        },
        enableAntiDetection: this.config.behavior.enableBehaviorSimulation,
        enableHealthMonitoring: true,
        enableSessionPersistence: true,
        maxRetries: this.config.retry.retryAttempts,
        ...options
      };

      // Initialize session through session manager
      const session = await this.sessionManager.createSession(sessionOptions);

      // Verify session is active
      if (!session.isActive) {
        throw new TwikitError(
          TwikitErrorType.SESSION_ERROR,
          'Session is not active after initialization',
          { sessionId: session.sessionId }
        );
      }

      // Update account info with session data
      await prisma.xAccount.update({
        where: { id: accountId },
        data: {
          lastActivity: new Date(),
          sessionId: session.sessionId,
          isActive: true
        }
      });

      logger.info(`Twikit session initialized successfully for account ${accountId}`, {
        sessionId: session.sessionId,
        accountId,
        isActive: session.isActive
      });

      return session;
    } catch (error) {
      logger.error(`Failed to initialize Twikit session for account ${accountId}:`, error);

      // Update account status on failure
      await prisma.xAccount.update({
        where: { id: accountId },
        data: {
          isActive: false,
          lastError: error instanceof Error ? error.message : 'Unknown error'
        }
      }).catch(() => {}); // Ignore database errors during error handling

      throw error;
    }
  }

  /**
   * Execute Twikit Action with Enterprise Features
   * Core method that handles all Twikit operations with comprehensive error handling,
   * rate limiting, anti-detection, and monitoring
   */
  async executeTwikitAction(request: TwikitActionRequest): Promise<TwikitActionResponse> {
    const startTime = Date.now();
    const { action, accountId, params, options = {}, metadata } = request;
    const correlationId = generateCorrelationId();

    try {
      // Enhanced logging with correlation ID and sanitized data
      logTwikitAction(action, accountId, sanitizeData(params), 'retry', {
        correlationId,
        attempt: 1,
        metadata: {
          ...metadata,
          requestId: correlationId,
          startTime: new Date(startTime).toISOString()
        }
      });

      // 1. Circuit Breaker Check with enhanced logging
      if (this.isCircuitBreakerOpen(accountId, action)) {
        const breakerKey = `${accountId}:${action}`;
        const breakerState = this.circuitBreakers.get(breakerKey);

        logCircuitBreaker('trip', breakerKey, {
          correlationId,
          accountId,
          failureCount: breakerState?.failures || 0,
          threshold: this.config.retry.circuitBreakerThreshold
        });

        throw new TwikitError(
          TwikitErrorType.CIRCUIT_BREAKER_OPEN,
          'Circuit breaker is open for this account/action combination',
          { accountId, action, correlationId, failureCount: breakerState?.failures }
        );
      }

      // 2. Rate Limit Check with enhanced logging
      if (!options.skipRateLimit) {
        const rateLimitCheck = await this.checkRateLimit(accountId, action, options.priority);

        const rateLimitContext: {
          correlationId: string;
          retryAfter?: number;
          priority?: string;
        } = {
          correlationId
        };

        if (rateLimitCheck.retryAfter !== undefined) {
          rateLimitContext.retryAfter = rateLimitCheck.retryAfter;
        }

        if (options.priority !== undefined) {
          rateLimitContext.priority = options.priority.toString();
        }

        logRateLimit(
          rateLimitCheck.allowed ? 'allowed' : 'blocked',
          action,
          accountId,
          rateLimitContext
        );

        if (!rateLimitCheck.allowed) {
          throw new TwikitError(
            TwikitErrorType.RATE_LIMIT_EXCEEDED,
            'Rate limit exceeded',
            { accountId, action, retryAfter: rateLimitCheck.retryAfter, correlationId }
          );
        }
      }

      // 3. Get or Create Session with enhanced logging
      let session = this.sessionManager.getSession(accountId);
      if (!session || !session.isActive) {
        logTwikitSession('created', '', accountId, {
          correlationId,
          metadata: { reason: session ? 'inactive_session' : 'no_session' }
        });
        session = await this.initializeTwikitSession(accountId);
      }

      // 4. Anti-Detection Coordination
      if (options.enableAntiDetection !== false && this.config.behavior.enableBehaviorSimulation) {
        // Anti-detection coordination would be implemented here
        // await this.antiDetectionCoordinator.coordinateAction(accountId, action, params);
      }

      // 5. Execute Action with Retry Logic
      const response = await this.executeWithRetry(session, action, params, options);
      const totalDuration = Date.now() - startTime;

      // 6. Update Performance Metrics
      this.updatePerformanceMetrics(accountId, action, totalDuration, true);

      // 7. Reset Circuit Breaker on Success
      this.resetCircuitBreaker(accountId, action);

      // 8. Enhanced Success Logging
      const successContext: {
        correlationId: string;
        sessionId: string;
        duration: number;
        metadata?: Record<string, any>;
      } = {
        correlationId,
        sessionId: session.sessionId,
        duration: totalDuration
      };

      if (metadata) {
        successContext.metadata = metadata as Record<string, any>;
      }

      logTwikitAction(action, accountId, sanitizeData(params), 'success', successContext);

      // 9. Log Performance Metrics
      logTwikitPerformance(action, accountId, {
        duration: totalDuration,
        success: true,
        retryCount: 0, // Will be updated if retries occurred
        correlationId
      });

      // 10. Audit Trail for Compliance
      if (metadata?.userId) {
        logAuditTrail(action, metadata.userId, accountId, {
          correlationId,
          result: 'success',
          resourceId: response?.id || response?.data?.id,
          metadata: sanitizeData(metadata)
        });
      }

      return {
        success: true,
        data: response,
        metadata: {
          sessionId: session.sessionId,
          executionTime: totalDuration
        }
      };

    } catch (error) {
      const totalDuration = Date.now() - startTime;
      const twikitError = error instanceof TwikitError ? error :
        new TwikitError(TwikitErrorType.UNKNOWN_ERROR, error instanceof Error ? error.message : 'Unknown error');

      // Enhanced Error Classification
      const errorCategory = this.categorizeError(twikitError);
      const isRetryable = this.isErrorRetryable(twikitError);

      // Update Circuit Breaker on Failure
      this.updateCircuitBreaker(accountId, action, twikitError);

      // Update Performance Metrics
      this.updatePerformanceMetrics(accountId, action, totalDuration, false);

      // Enhanced Error Logging
      logTwikitAction(action, accountId, sanitizeData(params), 'failure', {
        correlationId,
        duration: totalDuration,
        error: twikitError,
        metadata: {
          ...metadata,
          errorType: twikitError.code,
          errorCategory,
          isRetryable,
          stackTrace: twikitError.stack
        }
      });

      // Log Performance Metrics for Failure
      logTwikitPerformance(action, accountId, {
        duration: totalDuration,
        success: false,
        correlationId
      });

      // Audit Trail for Failed Actions
      if (metadata?.userId) {
        logAuditTrail(action, metadata.userId, accountId, {
          correlationId,
          result: 'failure',
          metadata: {
            errorType: twikitError.code,
            errorMessage: twikitError.message
          }
        });
      }

      const errorType = twikitError.code as TwikitErrorType;

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        errorType,
        metadata: {
          executionTime: Date.now() - startTime
        }
      };
    }
  }

  /**
   * Post content to X with comprehensive Twikit integration
   * Replaces Twitter API v2 postContent with Twikit create_tweet
   */
  async postContent(accountId: string, content: ContentData, options: PostOptions = {}, metadata?: { campaignId?: string; userId?: string; tags?: string[] }): Promise<TwikitActionResponse> {
    try {
      // Quality checks
      if (!options.skipQualityCheck) {
        const qualityCheck = await this.performQualityChecks(accountId, content);
        if (!qualityCheck.approved) {
          throw new TwikitError(
            TwikitErrorType.CONTENT_QUALITY_ERROR,
            `Quality check failed: ${qualityCheck.reason}`,
            { qualityCheck }
          );
        }
      }

      // Prepare Twikit parameters
      const twikitParams: Record<string, any> = {
        text: content.text
      };

      // Add media IDs if provided
      if (content.mediaIds && content.mediaIds.length > 0) {
        twikitParams.mediaIds = content.mediaIds;
      }

      // Add reply/quote information
      if (content.replyToTweetId) {
        twikitParams.replyToTweetId = content.replyToTweetId;
      }
      if (content.quoteTweetId) {
        twikitParams.quoteTweetId = content.quoteTweetId;
      }

      // Add poll if provided
      if (content.poll) {
        twikitParams.poll = content.poll;
      }

      // Execute Twikit action
      const response = await this.executeTwikitAction({
        action: TwikitActionType.CREATE_TWEET,
        accountId,
        params: twikitParams,
        options,
        metadata: {
          ...(metadata?.campaignId && { campaignId: metadata.campaignId }),
          ...(metadata?.userId && { userId: metadata.userId }),
          priority: options.priority || RateLimitPriority.NORMAL,
          tags: ['content_post', ...(metadata?.tags || [])]
        }
      });

      if (!response.success) {
        throw new TwikitError(
          response.errorType || TwikitErrorType.UNKNOWN_ERROR,
          response.error || 'Failed to post content',
          { response }
        );
      }

      // Log the post
      await this.logPost(accountId, content, response.data);

      logger.info(`Content posted successfully for account ${accountId}`, {
        accountId,
        tweetId: response.data?.id,
        text: content.text.substring(0, 50) + '...',
        executionTime: response.metadata?.executionTime
      });

      return response;

    } catch (error) {
      logger.error(`Failed to post content for account ${accountId}:`, error);
      throw error;
    }
  }

  /**
   * Perform quality checks on content
   */
  private async performQualityChecks(accountId: string, content: ContentData): Promise<QualityCheckResult> {
    try {
      let score = 1.0;
      const suggestions: string[] = [];

      // Check content length
      if (content.text.length < 10) {
        score -= 0.3;
        suggestions.push('Content is too short');
      }

      if (content.text.length > 280) {
        return {
          approved: false,
          score: 0,
          reason: 'Content exceeds 280 characters',
          suggestions: ['Shorten the content to fit Twitter\'s character limit']
        };
      }

      // Check for spam indicators
      const spamIndicators = ['!!!', 'URGENT', 'CLICK NOW', 'FREE MONEY'];
      const hasSpamIndicators = spamIndicators.some(indicator => 
        content.text.toUpperCase().includes(indicator)
      );

      if (hasSpamIndicators) {
        score -= 0.4;
        suggestions.push('Content contains potential spam indicators');
      }

      // Check hashtag count
      const hashtagCount = (content.hashtags || []).length;
      if (hashtagCount > 5) {
        score -= 0.2;
        suggestions.push('Too many hashtags (recommended: 1-3)');
      }

      // Check for appropriate content
      const inappropriateWords = ['hate', 'violence', 'scam'];
      const hasInappropriateContent = inappropriateWords.some(word => 
        content.text.toLowerCase().includes(word)
      );

      if (hasInappropriateContent) {
        return {
          approved: false,
          score: 0,
          reason: 'Content contains inappropriate language',
          suggestions: ['Remove inappropriate language']
        };
      }

      const approved = score >= this.config.quality.qualityThreshold;

      const result: QualityCheckResult = {
        approved,
        score,
      };

      if (!approved) {
        result.reason = 'Quality score below threshold';
      }

      if (suggestions.length > 0) {
        result.suggestions = suggestions;
      }

      return result;

    } catch (error) {
      logger.error('Quality check failed:', error);
      return {
        approved: false,
        score: 0,
        reason: 'Quality check system error'
      };
    }
  }

  /**
   * Check rate limits for account
   */
  private async checkRateLimits(accountId: string): Promise<RateLimitResult> {
    try {
      const now = new Date();
      const hourKey = `rate_limit:${accountId}:${now.getHours()}`;
      const dayKey = `rate_limit:${accountId}:${now.toDateString()}`;

      // Get current counts from cache
      const hourlyCount = await cacheManager.get<number>(hourKey) || 0;
      const dailyCount = await cacheManager.get<number>(dayKey) || 0;

      // Check hourly limit
      if (hourlyCount >= this.config.rateLimits.maxTweetsPerHour) {
        return {
          allowed: false,
          reason: 'Hourly post limit exceeded',
          retryAfter: 3600 - (now.getMinutes() * 60 + now.getSeconds())
        };
      }

      // Check daily limit (approximate based on hourly rate)
      const dailyLimit = this.config.rateLimits.maxTweetsPerHour * 24;
      if (dailyCount >= dailyLimit) {
        return {
          allowed: false,
          reason: 'Daily post limit exceeded',
          retryAfter: 86400 - (now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds())
        };
      }

      // Check minimum time between posts (15 minutes default)
      const lastPostKey = `last_post:${accountId}`;
      const lastPostTime = await cacheManager.get<number>(lastPostKey);
      const minTimeBetweenPosts = 15 * 60 * 1000; // 15 minutes

      if (lastPostTime && (now.getTime() - lastPostTime) < minTimeBetweenPosts) {
        return {
          allowed: false,
          reason: 'Minimum time between posts not met',
          retryAfter: Math.ceil((minTimeBetweenPosts - (now.getTime() - lastPostTime)) / 1000)
        };
      }

      return { allowed: true };

    } catch (error) {
      logger.error('Rate limit check failed:', error);
      return {
        allowed: false,
        reason: 'Rate limit check system error'
      };
    }
  }

  /**
   * Update rate limiting cache after posting
   */
  private async updateRateLimitCache(accountId: string): Promise<void> {
    try {
      const now = new Date();
      const hourKey = `rate_limit:${accountId}:${now.getHours()}`;
      const dayKey = `rate_limit:${accountId}:${now.toDateString()}`;
      const lastPostKey = `last_post:${accountId}`;

      // Increment counters
      await cacheManager.incrementRateLimit(hourKey, 3600000); // 1 hour
      await cacheManager.incrementRateLimit(dayKey, ********); // 24 hours
      
      // Update last post time
      await cacheManager.set(lastPostKey, now.getTime(), 86400); // 24 hours

    } catch (error) {
      logger.error('Failed to update rate limit cache:', error);
    }
  }

  /**
   * Log post to database
   */
  private async logPost(accountId: string, content: ContentData, result: any): Promise<void> {
    try {
      await prisma.post.create({
        data: {
          accountId,
          content: content.text,
          mediaUrls: content.mediaUrls || [],
          hashtags: content.hashtags || [],
          mentions: content.mentions || [],
          status: 'PUBLISHED',
          tweetId: result.data.id,
          publishedAt: new Date()
        }
      });
    } catch (error) {
      logger.error('Failed to log post to database:', error);
    }
  }

  /**
   * Get account automation status
   */
  async getAccountStatus(accountId: string): Promise<AutomationStatus | null> {
    try {
      // This would typically fetch from database or cache
      // For now, return a mock status
      return {
        isActive: this.activeAutomations.has(accountId),
        activeFeatures: [],
        lastActivity: new Date(),
        stats: {
          postsToday: 0,
          likesToday: 0,
          commentsToday: 0,
          followsToday: 0,
          retweetsToday: 0,
          dmsToday: 0,
          bookmarksToday: 0
        }
      };
    } catch (error) {
      logger.error('Failed to get account status:', error);
      return null;
    }
  }

  /**
   * Update settings for account
   */
  async updateSettings(accountId: string, settings: any): Promise<void> {
    try {
      // Store settings in cache or database
      await cacheManager.set(`settings:${accountId}`, settings, 86400);
      logger.info(`Settings updated for account ${accountId}`);
    } catch (error) {
      logger.error('Failed to update settings:', error);
    }
  }

  // ============================================================================
  // COMPREHENSIVE TWIKIT ACTION METHODS
  // ============================================================================

  /**
   * Like a tweet using Twikit
   */
  async likeTweet(accountId: string, tweetId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.LIKE_TWEET,
      accountId,
      params: { tweetId },
      options,
      metadata: { tags: ['engagement', 'like'] }
    });
  }

  /**
   * Unlike a tweet using Twikit
   */
  async unlikeTweet(accountId: string, tweetId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.UNLIKE_TWEET,
      accountId,
      params: { tweetId },
      options,
      metadata: { tags: ['engagement', 'unlike'] }
    });
  }

  /**
   * Retweet a tweet using Twikit
   */
  async retweet(accountId: string, tweetId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.RETWEET,
      accountId,
      params: { tweetId },
      options,
      metadata: { tags: ['engagement', 'retweet'] }
    });
  }

  /**
   * Unretweet a tweet using Twikit
   */
  async unretweet(accountId: string, tweetId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.UNRETWEET,
      accountId,
      params: { tweetId },
      options,
      metadata: { tags: ['engagement', 'unretweet'] }
    });
  }

  /**
   * Follow a user using Twikit
   */
  async followUser(accountId: string, userId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.FOLLOW_USER,
      accountId,
      params: { userId },
      options,
      metadata: { tags: ['social', 'follow'] }
    });
  }

  /**
   * Unfollow a user using Twikit
   */
  async unfollowUser(accountId: string, userId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.UNFOLLOW_USER,
      accountId,
      params: { userId },
      options,
      metadata: { tags: ['social', 'unfollow'] }
    });
  }

  /**
   * Block a user using Twikit
   */
  async blockUser(accountId: string, userId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.BLOCK_USER,
      accountId,
      params: { userId },
      options,
      metadata: { tags: ['moderation', 'block'] }
    });
  }

  /**
   * Unblock a user using Twikit
   */
  async unblockUser(accountId: string, userId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.UNBLOCK_USER,
      accountId,
      params: { userId },
      options,
      metadata: { tags: ['moderation', 'unblock'] }
    });
  }

  /**
   * Mute a user using Twikit
   */
  async muteUser(accountId: string, userId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.MUTE_USER,
      accountId,
      params: { userId },
      options,
      metadata: { tags: ['moderation', 'mute'] }
    });
  }

  /**
   * Unmute a user using Twikit
   */
  async unmuteUser(accountId: string, userId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.UNMUTE_USER,
      accountId,
      params: { userId },
      options,
      metadata: { tags: ['moderation', 'unmute'] }
    });
  }

  /**
   * Send a direct message using Twikit
   */
  async sendDirectMessage(accountId: string, userId: string, text: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.SEND_DM,
      accountId,
      params: { userId, text },
      options,
      metadata: { tags: ['messaging', 'dm'] }
    });
  }

  /**
   * Delete a tweet using Twikit
   */
  async deleteTweet(accountId: string, tweetId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.DELETE_TWEET,
      accountId,
      params: { tweetId },
      options,
      metadata: { tags: ['content', 'delete'] }
    });
  }

  /**
   * Bookmark a tweet using Twikit
   */
  async bookmarkTweet(accountId: string, tweetId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.BOOKMARK_TWEET,
      accountId,
      params: { tweetId },
      options,
      metadata: { tags: ['organization', 'bookmark'] }
    });
  }

  /**
   * Remove bookmark from a tweet using Twikit
   */
  async unbookmarkTweet(accountId: string, tweetId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.UNBOOKMARK_TWEET,
      accountId,
      params: { tweetId },
      options,
      metadata: { tags: ['organization', 'unbookmark'] }
    });
  }

  /**
   * Search tweets using Twikit
   */
  async searchTweets(accountId: string, query: string, searchType: 'Top' | 'Latest' | 'Media' = 'Latest', count: number = 20, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.SEARCH_TWEETS,
      accountId,
      params: { query, searchType, count },
      options,
      metadata: { tags: ['search', 'discovery'] }
    });
  }

  /**
   * Search users using Twikit
   */
  async searchUsers(accountId: string, query: string, count: number = 20, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.SEARCH_USER,
      accountId,
      params: { query, count },
      options,
      metadata: { tags: ['search', 'users'] }
    });
  }

  /**
   * Get user tweets using Twikit
   */
  async getUserTweets(accountId: string, userId: string, count: number = 20, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.GET_USER_TWEETS,
      accountId,
      params: { userId, count },
      options,
      metadata: { tags: ['content', 'user_tweets'] }
    });
  }

  /**
   * Get timeline using Twikit
   */
  async getTimeline(accountId: string, count: number = 20, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.GET_TIMELINE,
      accountId,
      params: { count },
      options,
      metadata: { tags: ['timeline', 'feed'] }
    });
  }

  /**
   * Get user by screen name using Twikit
   */
  async getUserByScreenName(accountId: string, screenName: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.GET_USER_BY_SCREEN_NAME,
      accountId,
      params: { screenName },
      options,
      metadata: { tags: ['user', 'profile'] }
    });
  }

  /**
   * Get user by ID using Twikit
   */
  async getUserById(accountId: string, userId: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.GET_USER_BY_ID,
      accountId,
      params: { userId },
      options,
      metadata: { tags: ['user', 'profile'] }
    });
  }

  /**
   * Get user followers using Twikit
   */
  async getUserFollowers(accountId: string, userId: string, count: number = 20, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.GET_USER_FOLLOWERS,
      accountId,
      params: { userId, count },
      options,
      metadata: { tags: ['social', 'followers'] }
    });
  }

  /**
   * Get user following using Twikit
   */
  async getUserFollowing(accountId: string, userId: string, count: number = 20, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.GET_USER_FOLLOWING,
      accountId,
      params: { userId, count },
      options,
      metadata: { tags: ['social', 'following'] }
    });
  }

  /**
   * Upload media using Twikit
   */
  async uploadMedia(accountId: string, mediaPath: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.UPLOAD_MEDIA,
      accountId,
      params: { mediaPath },
      options,
      metadata: { tags: ['media', 'upload'] }
    });
  }

  /**
   * Create poll using Twikit
   */
  async createPoll(accountId: string, text: string, choices: string[], durationMinutes: number, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.CREATE_POLL,
      accountId,
      params: { text, choices, durationMinutes },
      options,
      metadata: { tags: ['content', 'poll'] }
    });
  }

  /**
   * Vote in poll using Twikit
   */
  async votePoll(accountId: string, tweetId: string, choiceNumber: number, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.VOTE_POLL,
      accountId,
      params: { tweetId, choiceNumber },
      options,
      metadata: { tags: ['engagement', 'poll_vote'] }
    });
  }

  /**
   * Get trending topics using Twikit
   */
  async getTrends(accountId: string, location?: string, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.GET_TRENDS,
      accountId,
      params: { location },
      options,
      metadata: { tags: ['discovery', 'trends'] }
    });
  }

  /**
   * Get notifications using Twikit
   */
  async getNotifications(accountId: string, count: number = 20, options: PostOptions = {}): Promise<TwikitActionResponse> {
    return this.executeTwikitAction({
      action: TwikitActionType.GET_NOTIFICATIONS,
      accountId,
      params: { count },
      options,
      metadata: { tags: ['notifications', 'activity'] }
    });
  }

  /**
   * Stop all automation for account
   */
  async stopAllAutomation(accountId: string): Promise<void> {
    try {
      this.activeAutomations.delete(accountId);
      logger.info(`All automation stopped for account ${accountId}`);
    } catch (error) {
      logger.error('Failed to stop automation:', error);
    }
  }

  /**
   * Get Twikit session for account (replaces getClient)
   */
  async getTwikitSession(accountId: string): Promise<TwikitSession | null> {
    try {
      return this.sessionManager.getSession(accountId);
    } catch (error) {
      logger.error(`Failed to get Twikit session for account ${accountId}:`, error);
      return null;
    }
  }

  /**
   * Remove session for account (replaces removeClient)
   */
  async removeSession(accountId: string): Promise<void> {
    try {
      await this.sessionManager.destroySession(accountId);
      this.activeAutomations.delete(accountId);
      this.circuitBreakers.clear(); // Clear circuit breakers for this account
      this.performanceMetrics.clear(); // Clear performance metrics for this account
    } catch (error) {
      logger.error(`Failed to remove session for account ${accountId}:`, error);
    }
  }

  // ============================================================================
  // ENTERPRISE HELPER METHODS
  // ============================================================================

  /**
   * Sanitize parameters for logging (remove sensitive data)
   */
  private sanitizeParams(params: Record<string, any>): Record<string, any> {
    const sanitized = { ...params };
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth'];

    for (const key of Object.keys(sanitized)) {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * Check if circuit breaker is open for account/action combination
   */
  private isCircuitBreakerOpen(accountId: string, action: string): boolean {
    const key = `${accountId}:${action}`;
    const breaker = this.circuitBreakers.get(key);

    if (!breaker) return false;

    // Check if breaker should be reset (after timeout)
    const resetTime = breaker.lastFailure.getTime() + (5 * 60 * 1000); // 5 minutes
    if (Date.now() > resetTime) {
      this.circuitBreakers.delete(key);
      return false;
    }

    return breaker.isOpen;
  }



  /**
   * Check rate limits using GlobalRateLimitCoordinator
   */
  private async checkRateLimit(accountId: string, action: string, priority?: RateLimitPriority): Promise<{ allowed: boolean; retryAfter?: number }> {
    try {
      const rateLimitAction = this.mapActionToRateLimitAction(action);
      const request: RateLimitCheckRequest = {
        accountId,
        action: rateLimitAction,
        priority: priority || RateLimitPriority.NORMAL,
        accountType: AccountType.STANDARD // Could be determined from account data
      };

      const response = await this.rateLimitCoordinator.checkRateLimit(request);
      const result: { allowed: boolean; retryAfter?: number } = {
        allowed: response.allowed
      };

      if (response.retryAfter) {
        result.retryAfter = Math.floor((response.retryAfter.getTime() - Date.now()) / 1000);
      }

      return result;
    } catch (error) {
      logger.error('Rate limit check failed:', error);
      return { allowed: true }; // Fail open for now
    }
  }

  /**
   * Map Twikit action to rate limit action
   */
  private mapActionToRateLimitAction(action: string): RateLimitAction {
    const actionMap: Record<string, RateLimitAction> = {
      [TwikitActionType.CREATE_TWEET]: RateLimitAction.POST_TWEET,
      [TwikitActionType.LIKE_TWEET]: RateLimitAction.LIKE_TWEET,
      [TwikitActionType.RETWEET]: RateLimitAction.RETWEET,
      [TwikitActionType.FOLLOW_USER]: RateLimitAction.FOLLOW_USER,
      [TwikitActionType.SEND_DM]: RateLimitAction.SEND_DM,
      [TwikitActionType.SEARCH_TWEETS]: RateLimitAction.SEARCH,
      [TwikitActionType.GET_USER_TWEETS]: RateLimitAction.GET_TWEETS,
      [TwikitActionType.GET_TIMELINE]: RateLimitAction.GET_TIMELINE
    };

    return actionMap[action] || RateLimitAction.GENERAL;
  }

  /**
   * Execute action with enterprise retry logic and process management
   */
  private async executeWithRetry(session: TwikitSession, action: string, params: Record<string, any>, options: PostOptions): Promise<any> {
    // Use the intelligent retry engine for better error handling
    const retryResult = await this.retryEngine.executeWithRetry(
      async () => {
        // Execute through enterprise session manager with process pooling
        return await this.sessionManager.executeAction(session.accountId, action, params);
      },
      `${action}_${session.accountId}`,
      {
        maxAttempts: this.config.retry.retryAttempts,
        baseDelay: this.config.retry.retryDelay,
        maxDelay: this.config.retry.maxRetryDelay,
        backoffMultiplier: this.config.retry.exponentialBackoff ? 2 : 1,
        enableCircuitBreaker: true,
        circuitBreakerThreshold: this.config.retry.circuitBreakerThreshold
      }
    );

    if (retryResult.success) {
      return retryResult.result;
    } else {
      throw retryResult.error || new Error('Action execution failed after retries');
    }
  }

  /**
   * Update performance metrics
   */
  private updatePerformanceMetrics(accountId: string, action: string, responseTime: number, success: boolean): void {
    const key = `${accountId}:${action}`;
    const metrics = this.performanceMetrics.get(key) || { totalRequests: 0, successRate: 0, avgResponseTime: 0 };

    metrics.totalRequests++;
    metrics.successRate = success
      ? (metrics.successRate * (metrics.totalRequests - 1) + 1) / metrics.totalRequests
      : (metrics.successRate * (metrics.totalRequests - 1)) / metrics.totalRequests;
    metrics.avgResponseTime = (metrics.avgResponseTime * (metrics.totalRequests - 1) + responseTime) / metrics.totalRequests;

    this.performanceMetrics.set(key, metrics);
  }

  /**
   * Categorize error for enhanced logging and handling
   */
  private categorizeError(error: TwikitError): string {
    const errorType = error.code as TwikitErrorType;

    // Authentication & Account Errors
    if ([
      TwikitErrorType.AUTHENTICATION_ERROR,
      TwikitErrorType.AUTHENTICATION_FAILED,
      TwikitErrorType.ACCOUNT_LOCKED,
      TwikitErrorType.ACCOUNT_SUSPENDED,
      TwikitErrorType.ACCOUNT_RESTRICTED,
      TwikitErrorType.INVALID_CREDENTIALS,
      TwikitErrorType.TWO_FACTOR_REQUIRED
    ].includes(errorType)) {
      return 'AUTHENTICATION';
    }

    // Session Errors
    if ([
      TwikitErrorType.SESSION_ERROR,
      TwikitErrorType.SESSION_CREATION_FAILED,
      TwikitErrorType.SESSION_EXPIRED,
      TwikitErrorType.SESSION_INVALID,
      TwikitErrorType.SESSION_LIMIT_EXCEEDED
    ].includes(errorType)) {
      return 'SESSION';
    }

    // Content Errors
    if ([
      TwikitErrorType.CONTENT_QUALITY_ERROR,
      TwikitErrorType.CONTENT_TOO_LONG,
      TwikitErrorType.CONTENT_DUPLICATE,
      TwikitErrorType.CONTENT_SPAM_DETECTED,
      TwikitErrorType.CONTENT_POLICY_VIOLATION,
      TwikitErrorType.MEDIA_UPLOAD_FAILED,
      TwikitErrorType.MEDIA_FORMAT_UNSUPPORTED
    ].includes(errorType)) {
      return 'CONTENT';
    }

    // Rate Limiting Errors
    if ([
      TwikitErrorType.RATE_LIMIT_EXCEEDED,
      TwikitErrorType.RATE_LIMIT_TWEET,
      TwikitErrorType.RATE_LIMIT_FOLLOW,
      TwikitErrorType.RATE_LIMIT_LIKE,
      TwikitErrorType.RATE_LIMIT_RETWEET,
      TwikitErrorType.RATE_LIMIT_DM,
      TwikitErrorType.RATE_LIMIT_SEARCH
    ].includes(errorType)) {
      return 'RATE_LIMIT';
    }

    // Network & Infrastructure Errors
    if ([
      TwikitErrorType.PROXY_ERROR,
      TwikitErrorType.PROXY_CONNECTION_FAILED,
      TwikitErrorType.PROXY_AUTHENTICATION_FAILED,
      TwikitErrorType.PROXY_TIMEOUT,
      TwikitErrorType.PROXY_BLOCKED,
      TwikitErrorType.NETWORK_ERROR,
      TwikitErrorType.CONNECTION_TIMEOUT,
      TwikitErrorType.DNS_RESOLUTION_FAILED
    ].includes(errorType)) {
      return 'NETWORK';
    }

    // Anti-Detection Errors
    if ([
      TwikitErrorType.DETECTION_RISK_HIGH,
      TwikitErrorType.CAPTCHA_REQUIRED,
      TwikitErrorType.SUSPICIOUS_ACTIVITY,
      TwikitErrorType.FINGERPRINT_MISMATCH,
      TwikitErrorType.BEHAVIOR_ANOMALY
    ].includes(errorType)) {
      return 'ANTI_DETECTION';
    }

    // System Errors
    if ([
      TwikitErrorType.CIRCUIT_BREAKER_OPEN,
      TwikitErrorType.TIMEOUT_ERROR,
      TwikitErrorType.PYTHON_PROCESS_ERROR,
      TwikitErrorType.PYTHON_IMPORT_ERROR,
      TwikitErrorType.CONFIGURATION_ERROR
    ].includes(errorType)) {
      return 'SYSTEM';
    }

    return 'UNKNOWN';
  }

  /**
   * Determine if error is retryable based on type and context
   */
  private isErrorRetryable(error: TwikitError): boolean {
    const errorType = error.code as TwikitErrorType;

    // Never retry these errors
    const nonRetryableErrors = [
      TwikitErrorType.ACCOUNT_SUSPENDED,
      TwikitErrorType.ACCOUNT_LOCKED,
      TwikitErrorType.ACCOUNT_RESTRICTED,
      TwikitErrorType.AUTHENTICATION_FAILED,
      TwikitErrorType.INVALID_CREDENTIALS,
      TwikitErrorType.CONTENT_POLICY_VIOLATION,
      TwikitErrorType.CONTENT_DUPLICATE,
      TwikitErrorType.CAPTCHA_REQUIRED,
      TwikitErrorType.DETECTION_RISK_HIGH,
      TwikitErrorType.SUSPICIOUS_ACTIVITY,
      TwikitErrorType.CIRCUIT_BREAKER_OPEN
    ];

    if (nonRetryableErrors.includes(errorType)) {
      return false;
    }

    // Always retry these errors (with limits)
    const retryableErrors = [
      TwikitErrorType.NETWORK_ERROR,
      TwikitErrorType.CONNECTION_TIMEOUT,
      TwikitErrorType.PROXY_ERROR,
      TwikitErrorType.PROXY_CONNECTION_FAILED,
      TwikitErrorType.PROXY_TIMEOUT,
      TwikitErrorType.TIMEOUT_ERROR,
      TwikitErrorType.SESSION_ERROR,
      TwikitErrorType.SESSION_EXPIRED,
      TwikitErrorType.PYTHON_PROCESS_ERROR
    ];

    if (retryableErrors.includes(errorType)) {
      return true;
    }

    // Rate limit errors are retryable but with special handling
    if (errorType.toString().includes('RATE_LIMIT')) {
      return true;
    }

    // Default to not retryable for unknown errors
    return false;
  }

  /**
   * Enhanced circuit breaker update with error context
   */
  private updateCircuitBreaker(accountId: string, action: string, error?: Error): void {
    const key = `${accountId}:${action}`;
    const breaker = this.circuitBreakers.get(key) || { failures: 0, lastFailure: new Date(), isOpen: false };

    breaker.failures++;
    breaker.lastFailure = new Date();
    breaker.isOpen = breaker.failures >= this.config.retry.circuitBreakerThreshold;

    this.circuitBreakers.set(key, breaker);

    // Log circuit breaker state change
    if (breaker.isOpen) {
      const logContext: {
        accountId: string;
        failureCount: number;
        threshold: number;
        error?: Error;
      } = {
        accountId,
        failureCount: breaker.failures,
        threshold: this.config.retry.circuitBreakerThreshold
      };

      if (error) {
        logContext.error = error;
      }

      logCircuitBreaker('opened', key, logContext);
    }
  }

  /**
   * Enhanced circuit breaker reset with logging
   */
  private resetCircuitBreaker(accountId: string, action: string): void {
    const key = `${accountId}:${action}`;
    const wasOpen = this.circuitBreakers.get(key)?.isOpen || false;

    this.circuitBreakers.delete(key);

    if (wasOpen) {
      logCircuitBreaker('closed', key, {
        accountId
      });
    }
  }

  /**
   * Sleep utility for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
