# WebSocket service Dockerfile
FROM node:18-alpine AS base

# Install system dependencies
RUN apk update && apk upgrade && \
    apk add --no-cache \
    curl \
    dumb-init \
    tzdata \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Set timezone
ENV TZ=UTC

# Create app user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY prisma ./prisma/

# Install dependencies
FROM base AS deps
RUN npm ci --only=production && npm cache clean --force

# Build stage
FROM base AS builder
COPY package*.json ./
RUN npm ci
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build WebSocket service
RUN npm run build:websocket

# Remove dev dependencies
RUN npm prune --production

# Production stage
FROM base AS runner

# Set production environment
ENV NODE_ENV=production
ENV PORT=3001

# Copy built WebSocket service
COPY --from=builder --chown=nextjs:nodejs /app/dist/websocket ./dist
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma

# Create necessary directories
RUN mkdir -p /app/logs && \
    chown -R nextjs:nodejs /app/logs

# Copy health check script
COPY --chown=nextjs:nodejs docker/scripts/websocket-healthcheck.sh /app/healthcheck.sh
RUN chmod +x /app/healthcheck.sh

# Switch to non-root user
USER nextjs

# Expose WebSocket port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD /app/healthcheck.sh

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start WebSocket service
CMD ["node", "dist/websocket-server.js"]
