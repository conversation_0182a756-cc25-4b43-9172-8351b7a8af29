# User Management Service Environment Template
# Copy this file to .env.local and fill in the actual values

# Application Configuration
NODE_ENV=development
PORT=3011
HOST=localhost

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/x_marketing_platform
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_KEY_PREFIX=user-mgmt:

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=user-management-service
KAFKA_GROUP_ID=user-management-group
DISABLE_KAFKA=false

# Consul Configuration
CONSUL_HOST=localhost
CONSUL_PORT=8500
CONSUL_SERVICE_NAME=user-management-service
DISABLE_CONSUL=false

# JWT Configuration (REQUIRED - Generate secure keys)
JWT_SECRET=your-super-secret-jwt-key-that-should-be-at-least-32-characters-long
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key-that-should-be-at-least-32-characters-long
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=10

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9091
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Service Discovery
SERVICE_VERSION=1.0.0
SERVICE_TAGS=user-management,authentication,authorization,microservice

# Health Checks
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Enterprise Features
ENTERPRISE_MODE=true
ENABLE_AUDIT_LOGGING=true
ENABLE_SECURITY_MONITORING=true

# User Management Specific
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true
SESSION_TIMEOUT=3600000

# External Services
ACCOUNT_MANAGEMENT_SERVICE_URL=http://localhost:3012
CAMPAIGN_MANAGEMENT_SERVICE_URL=http://localhost:3013
BACKEND_SERVICE_URL=http://localhost:3001
LLM_SERVICE_URL=http://localhost:3003
TELEGRAM_BOT_URL=http://localhost:3002

# Email Configuration (Optional)
EMAIL_ENABLED=false
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_SECURE=false
EMAIL_SMTP_USER=<EMAIL>
EMAIL_SMTP_PASS=your-app-password

# SMS Configuration (Optional)
SMS_ENABLED=false
SMS_PROVIDER=twilio
SMS_ACCOUNT_SID=your-twilio-account-sid
SMS_AUTH_TOKEN=your-twilio-auth-token
SMS_FROM_NUMBER=+**********

# Social Login (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
