# Comprehensive Test Configuration
# Testing Excellence for X/Twitter Automation Platform

# Global test configuration
global:
  version: "1.0"
  coverage_threshold: 95
  test_timeout: 1800  # 30 minutes
  parallel_jobs: 4
  retry_attempts: 3
  
# Test suite configurations
test_suites:
  unit:
    description: "Unit tests for individual components and functions"
    timeout: 300  # 5 minutes
    coverage_threshold: 95
    parallel: true
    retry_on_failure: true
    
  integration:
    description: "Integration tests for service interactions"
    timeout: 600  # 10 minutes
    coverage_threshold: 90
    parallel: true
    requires_database: true
    requires_redis: true
    
  e2e:
    description: "End-to-end tests for complete user workflows"
    timeout: 1800  # 30 minutes
    coverage_threshold: 80
    parallel: false
    requires_full_stack: true
    browsers: ["chromium", "firefox", "webkit"]
    
  api_contract:
    description: "API contract tests for service boundaries"
    timeout: 600  # 10 minutes
    coverage_threshold: 85
    parallel: true
    requires_pact_broker: true
    
  performance:
    description: "Performance and load testing"
    timeout: 1200  # 20 minutes
    parallel: false
    requires_full_stack: true
    load_test_duration: 300  # 5 minutes
    
  accessibility:
    description: "Accessibility compliance testing"
    timeout: 900  # 15 minutes
    parallel: true
    wcag_level: "AA"
    color_contrast_ratio: 4.5
    
  security:
    description: "Security vulnerability testing"
    timeout: 1200  # 20 minutes
    parallel: true
    requires_security_tools: true
    
  twikit:
    description: "Twikit integration specific tests"
    timeout: 900  # 15 minutes
    parallel: false
    requires_database: true
    requires_redis: true
    requires_proxy_pool: true

# Service-specific test configurations
services:
  backend:
    framework: "jest"
    language: "javascript"
    runtime: "node"
    versions: ["18", "20"]
    
    # Test types and their configurations
    tests:
      unit:
        pattern: "tests/unit/**/*.test.js"
        setup: "tests/setup/unit.js"
        coverage_threshold: 95
        
      integration:
        pattern: "tests/integration/**/*.test.js"
        setup: "tests/setup/integration.js"
        coverage_threshold: 90
        requires_database: true
        
      api_contract:
        pattern: "tests/contract/**/*.test.js"
        setup: "tests/setup/contract.js"
        pact_dir: "pacts"
        
    # Dependencies for testing
    dependencies:
      - "jest"
      - "@jest/globals"
      - "supertest"
      - "@pact-foundation/pact"
      - "testcontainers"
      - "@testcontainers/postgresql"
      
    # Database configuration
    database:
      type: "postgresql"
      host: "localhost"
      port: 5432
      name: "testdb"
      user: "testuser"
      password: "testpass"
      
  frontend:
    framework: "jest"
    language: "javascript"
    runtime: "node"
    versions: ["18", "20"]
    
    # Test types and their configurations
    tests:
      unit:
        pattern: "tests/unit/**/*.test.js"
        setup: "tests/setup/unit.js"
        coverage_threshold: 95
        environment: "jsdom"
        
      integration:
        pattern: "tests/integration/**/*.test.js"
        setup: "tests/setup/integration.js"
        coverage_threshold: 90
        
      e2e:
        pattern: "tests/e2e/**/*.spec.js"
        setup: "tests/setup/e2e.js"
        browsers: ["chromium", "firefox", "webkit"]
        
      accessibility:
        pattern: "tests/a11y/**/*.test.js"
        setup: "tests/setup/a11y.js"
        wcag_level: "AA"
        
    # Dependencies for testing
    dependencies:
      - "jest"
      - "@testing-library/react"
      - "@testing-library/jest-dom"
      - "@testing-library/user-event"
      - "@playwright/test"
      - "@axe-core/playwright"
      - "lighthouse"
      
  telegram-bot:
    framework: "jest"
    language: "javascript"
    runtime: "node"
    versions: ["18", "20"]
    
    # Test types and their configurations
    tests:
      unit:
        pattern: "tests/unit/**/*.test.js"
        setup: "tests/setup/unit.js"
        coverage_threshold: 95
        
      integration:
        pattern: "tests/integration/**/*.test.js"
        setup: "tests/setup/integration.js"
        coverage_threshold: 90
        requires_redis: true
        
      webhook:
        pattern: "tests/webhook/**/*.test.js"
        setup: "tests/setup/webhook.js"
        mock_telegram_api: true
        
    # Dependencies for testing
    dependencies:
      - "jest"
      - "supertest"
      - "nock"
      - "@testcontainers/redis"
      
    # Redis configuration
    redis:
      host: "localhost"
      port: 6379
      database: 0
      
  llm-service:
    framework: "pytest"
    language: "python"
    runtime: "python"
    versions: ["3.9", "3.11"]
    
    # Test types and their configurations
    tests:
      unit:
        pattern: "tests/unit/"
        setup: "tests/conftest.py"
        coverage_threshold: 95
        
      integration:
        pattern: "tests/integration/"
        setup: "tests/conftest.py"
        coverage_threshold: 90
        requires_database: true
        requires_redis: true
        
      twikit:
        pattern: "tests/twikit/"
        setup: "tests/conftest.py"
        coverage_threshold: 90
        requires_proxy_pool: true
        
      performance:
        pattern: "tests/performance/"
        setup: "tests/conftest.py"
        load_test_users: 100
        duration: 300
        
    # Dependencies for testing
    dependencies:
      - "pytest"
      - "pytest-cov"
      - "pytest-asyncio"
      - "pytest-mock"
      - "httpx"
      - "factory-boy"
      - "responses"
      - "locust"
      
    # Database configuration
    database:
      type: "postgresql"
      host: "localhost"
      port: 5432
      name: "testdb"
      user: "testuser"
      password: "testpass"
      
    # Redis configuration
    redis:
      host: "localhost"
      port: 6379
      database: 0

# Cross-platform testing configuration
cross_platform:
  # Free hosting platforms to test
  platforms:
    vercel:
      suitable_for: ["frontend"]
      test_url_pattern: "https://{service}-{branch}.vercel.app"
      health_check_path: "/health"
      
    netlify:
      suitable_for: ["frontend", "telegram-bot"]
      test_url_pattern: "https://{service}-{branch}.netlify.app"
      health_check_path: "/health"
      
    railway:
      suitable_for: ["backend", "telegram-bot", "llm-service"]
      test_url_pattern: "https://{service}-{branch}.railway.app"
      health_check_path: "/health"
      
    render:
      suitable_for: ["backend", "frontend", "telegram-bot", "llm-service"]
      test_url_pattern: "https://{service}-{branch}.onrender.com"
      health_check_path: "/health"
      
    fly_io:
      suitable_for: ["backend", "telegram-bot", "llm-service"]
      test_url_pattern: "https://{service}-{branch}.fly.dev"
      health_check_path: "/health"
      
  # Browser compatibility testing
  browsers:
    chromium:
      versions: ["latest", "stable"]
      mobile_emulation: true
      
    firefox:
      versions: ["latest", "esr"]
      mobile_emulation: true
      
    webkit:
      versions: ["latest"]
      mobile_emulation: true
      
    edge:
      versions: ["latest"]
      mobile_emulation: false
      
  # Runtime version testing
  runtimes:
    nodejs:
      versions: ["18.x", "20.x"]
      test_matrix: true
      
    python:
      versions: ["3.9", "3.11"]
      test_matrix: true

# Performance benchmarks and SLAs
performance:
  # API response time SLAs
  api_sla:
    backend:
      health_check: 100  # ms
      authentication: 500  # ms
      data_retrieval: 1000  # ms
      data_mutation: 2000  # ms
      
    llm_service:
      health_check: 200  # ms
      model_inference: 5000  # ms
      twikit_operation: 3000  # ms
      
    telegram_bot:
      webhook_response: 500  # ms
      command_processing: 2000  # ms
      
  # Frontend performance SLAs
  frontend_sla:
    first_contentful_paint: 1500  # ms
    largest_contentful_paint: 2500  # ms
    cumulative_layout_shift: 0.1
    first_input_delay: 100  # ms
    
  # Load testing parameters
  load_testing:
    concurrent_users: 100
    ramp_up_time: 60  # seconds
    test_duration: 300  # seconds
    think_time: 1  # seconds
    
    # Scenarios
    scenarios:
      - name: "normal_load"
        users: 50
        duration: 300
        
      - name: "peak_load"
        users: 100
        duration: 180
        
      - name: "stress_test"
        users: 200
        duration: 120

# Accessibility testing configuration
accessibility:
  # WCAG compliance level
  wcag_level: "AA"
  wcag_version: "2.1"
  
  # Color contrast requirements
  color_contrast:
    normal_text: 4.5
    large_text: 3.0
    
  # Automated testing tools
  tools:
    - "axe-core"
    - "lighthouse"
    - "pa11y"
    - "wave"
    
  # Test categories
  categories:
    - "keyboard_navigation"
    - "screen_reader"
    - "color_contrast"
    - "focus_management"
    - "semantic_html"
    - "alternative_text"
    - "form_labels"
    - "heading_structure"

# Security testing configuration
security:
  # Security scanning tools
  tools:
    - "snyk"
    - "semgrep"
    - "bandit"  # Python
    - "eslint-plugin-security"  # JavaScript
    
  # Vulnerability categories
  categories:
    - "sql_injection"
    - "xss"
    - "csrf"
    - "authentication_bypass"
    - "authorization_flaws"
    - "sensitive_data_exposure"
    - "security_misconfiguration"
    - "insecure_dependencies"
    
  # Security test scenarios
  scenarios:
    - name: "authentication_tests"
      description: "Test authentication and session management"
      
    - name: "authorization_tests"
      description: "Test access control and permissions"
      
    - name: "input_validation_tests"
      description: "Test input sanitization and validation"
      
    - name: "twikit_security_tests"
      description: "Test Twikit integration security"

# Twikit-specific testing configuration
twikit:
  # Test scenarios
  scenarios:
    session_management:
      description: "Test session creation, persistence, and cleanup"
      test_cases:
        - "session_creation"
        - "session_persistence"
        - "session_encryption"
        - "session_cleanup"
        - "concurrent_sessions"
        
    rate_limiting:
      description: "Test rate limiting compliance and coordination"
      test_cases:
        - "rate_limit_enforcement"
        - "rate_limit_coordination"
        - "burst_handling"
        - "cooldown_periods"
        
    proxy_rotation:
      description: "Test proxy pool management and rotation"
      test_cases:
        - "proxy_health_checks"
        - "proxy_rotation_logic"
        - "proxy_failure_handling"
        - "proxy_pool_management"
        
    anti_detection:
      description: "Test anti-detection mechanisms"
      test_cases:
        - "fingerprint_randomization"
        - "behavioral_mimicry"
        - "timing_patterns"
        - "detection_monitoring"
        
  # Mock configurations
  mocks:
    twitter_api: true
    proxy_pool: true
    rate_limiter: false  # Test with real rate limiting
    
  # Test data
  test_data:
    accounts: 5
    tweets: 100
    proxies: 10

# Test data management
test_data:
  # Database seeding
  database:
    seed_data: true
    cleanup_after_tests: true
    isolation_level: "test_case"
    
  # File-based test data
  fixtures:
    location: "tests/fixtures/"
    format: "json"
    
  # Factory configurations
  factories:
    user_factory: "tests/factories/user.py"
    tweet_factory: "tests/factories/tweet.py"
    session_factory: "tests/factories/session.py"

# Test reporting and notifications
reporting:
  # Coverage reporting
  coverage:
    formats: ["text", "html", "xml", "lcov"]
    output_dir: "coverage/"
    fail_under: 95
    
  # Test result formats
  test_results:
    formats: ["junit", "json", "html"]
    output_dir: "test-results/"
    
  # Notifications
  notifications:
    slack:
      enabled: true
      webhook_url: "${SLACK_WEBHOOK_URL}"
      channels: ["#testing", "#ci-cd"]
      
    email:
      enabled: true
      recipients: ["<EMAIL>"]
      
    github:
      enabled: true
      pr_comments: true
      check_runs: true

# CI/CD integration
ci_cd:
  # Trigger conditions
  triggers:
    - "push"
    - "pull_request"
    - "schedule"
    - "workflow_dispatch"
    
  # Caching strategy
  caching:
    dependencies: true
    test_results: true
    coverage_reports: true
    
  # Parallel execution
  parallelization:
    enabled: true
    max_jobs: 4
    strategy: "service_based"
    
  # Artifact retention
  artifacts:
    test_results: 30  # days
    coverage_reports: 30  # days
    failure_screenshots: 7  # days
    performance_reports: 90  # days
