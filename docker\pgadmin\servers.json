{"Servers": {"1": {"Name": "Twikit PostgreSQL", "Group": "Servers", "Host": "postgres-xmarketing", "Port": 5432, "MaintenanceDB": "x_marketing_platform", "Username": "postgres", "Password": "postgres_secure_2024", "SSLMode": "prefer", "SSLCert": "<STORAGE_DIR>/.postgresql/postgresql.crt", "SSLKey": "<STORAGE_DIR>/.postgresql/postgresql.key", "SSLRootCert": "<STORAGE_DIR>/.postgresql/root.crt", "SSLCrl": "<STORAGE_DIR>/.postgresql/root.crl", "SSLCompression": 0, "Timeout": 10, "UseSSHTunnel": 0, "TunnelHost": "", "TunnelPort": "22", "TunnelUsername": "", "TunnelAuthentication": 0, "BGColor": "#3498db", "FGColor": "#ffffff", "Service": "", "Shared": false, "SharedUsername": "", "Comment": "Twikit X/Twitter Automation Database - Development Environment"}}}