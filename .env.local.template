# X Marketing Platform - Local Environment Configuration
# Copy this file to .env.local and fill in your actual values

# ===================================
# AVAILABLE API CREDENTIALS
# ===================================

# Telegram Bot Token (AVAILABLE)
TELEGRAM_BOT_TOKEN=**********************************************

# Hugging Face API Key (AVAILABLE)
HUGGINGFACE_API_KEY=*************************************

# ===================================
# DATABASE CONFIGURATION
# ===================================

# PostgreSQL Database (Local)
DATABASE_URL=postgresql://x_marketing_user:secure_password_123@localhost:5432/x_marketing_platform

# Redis Cache (Local)
REDIS_URL=redis://localhost:6379

# ===================================
# SECURITY CONFIGURATION
# ===================================

# JWT Secrets (Generate new ones for production)
JWT_SECRET=your-super-secure-jwt-secret-min-32-chars-local-dev
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-min-32-chars-local-dev
ENCRYPTION_KEY=your-32-character-encryption-key-local-dev

# ===================================
# X/TWITTER API (NOT AVAILABLE)
# ===================================
# Note: These are not available due to regional restrictions
# The platform will work in content generation mode without these

X_API_KEY=not-available-regional-restrictions
X_API_SECRET=not-available-regional-restrictions
X_BEARER_TOKEN=not-available-regional-restrictions
X_ACCESS_TOKEN=not-available-regional-restrictions
X_ACCESS_TOKEN_SECRET=not-available-regional-restrictions

# ===================================
# APPLICATION URLS
# ===================================

# Local Development URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001
LLM_SERVICE_URL=http://localhost:3003
TELEGRAM_BOT_URL=http://localhost:3002
TELEGRAM_WEBHOOK_URL=http://localhost:3002/webhook

# LLM Service Configuration
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODELS=llama2,codellama,mistral

# ===================================
# ENVIRONMENT SETTINGS
# ===================================

NODE_ENV=development
FLASK_ENV=development
FLASK_DEBUG=true
LOG_LEVEL=debug

# Service Ports
BACKEND_PORT=3001
FRONTEND_PORT=3000
TELEGRAM_PORT=3002
LLM_SERVICE_PORT=3003

# ===================================
# FEATURE FLAGS
# ===================================

# Enable advanced features
ENABLE_ADVANCED_FEATURES=true

# Content generation features
ENABLE_HUGGINGFACE_INTEGRATION=true
ENABLE_MULTIMODAL_CONTENT=true
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_CONTENT_OPTIMIZATION=true

# Browser assistant features
ENABLE_BROWSER_ASSISTANT=true
ENABLE_CONTENT_SUGGESTIONS=true
ENABLE_COMPLIANCE_CHECKING=true

# Compliance and safety
COMPLIANCE_STRICT_MODE=true
ENABLE_CONTENT_FILTERING=true
ENABLE_RATE_LIMITING=true
ENABLE_AUDIT_LOGGING=true

# Development features
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_DEBUG_LOGGING=true

# ===================================
# PERFORMANCE SETTINGS
# ===================================

# Rate Limiting (Development - More Permissive)
MAX_ACCOUNTS_PER_USER=50
MAX_DAILY_ACTIONS_PER_ACCOUNT=200
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Cache Settings
CACHE_TTL_SECONDS=3600
REDIS_CONNECTION_POOL_SIZE=5

# Database Settings
DB_CONNECTION_POOL_SIZE=10
DB_QUERY_TIMEOUT=30000

# LLM Service Settings
LLM_REQUEST_TIMEOUT=60
LLM_MAX_CONCURRENT_REQUESTS=10
LLM_RETRY_ATTEMPTS=3

# ===================================
# CONTENT GENERATION SETTINGS
# ===================================

# Default content parameters
DEFAULT_CONTENT_TONE=professional
DEFAULT_CONTENT_LENGTH=280
DEFAULT_PLATFORM=twitter

# Hugging Face Model Preferences
HF_DEFAULT_TEXT_MODEL=mistral
HF_DEFAULT_IMAGE_MODEL=stable_diffusion
HF_DEFAULT_SENTIMENT_MODEL=roberta

# Content Quality Thresholds
MIN_QUALITY_SCORE=0.7
MIN_COMPLIANCE_SCORE=0.8
MAX_CONTENT_LENGTH=280

# ===================================
# BROWSER ASSISTANT SETTINGS
# ===================================

# Browser extension settings
BROWSER_ASSISTANT_ENABLED=true
CONTENT_SUGGESTIONS_ENABLED=true
REAL_TIME_ANALYSIS_ENABLED=true

# Assistant behavior
ASSISTANT_AUTO_SHOW=false
ASSISTANT_POSITION=bottom-right
ASSISTANT_THEME=light

# ===================================
# MONITORING & ANALYTICS (OPTIONAL)
# ===================================

# Monitoring services (optional)
GRAFANA_API_KEY=optional-for-monitoring
SENTRY_DSN=optional-for-error-tracking
GOOGLE_ANALYTICS_ID=optional-for-analytics

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_TRACKING=true
ENABLE_USAGE_ANALYTICS=false

# ===================================
# DEVELOPMENT TOOLS
# ===================================

# Development database tools
ENABLE_PRISMA_STUDIO=true
PRISMA_STUDIO_PORT=5555

# API documentation
ENABLE_API_DOCS=true
API_DOCS_PATH=/api-docs

# Development utilities
ENABLE_HOT_RELOAD=true
ENABLE_SOURCE_MAPS=true
ENABLE_VERBOSE_LOGGING=true

# ===================================
# COMPLIANCE SETTINGS
# ===================================

# Content compliance
PROHIBITED_WORDS=guaranteed,100%,risk-free,get rich quick,pump,dump,moon,lambo
REQUIRED_DISCLAIMERS=true
AUTO_ADD_DISCLAIMERS=true

# Platform compliance
RESPECT_RATE_LIMITS=true
HUMAN_LIKE_BEHAVIOR=true
AVOID_SPAM_PATTERNS=true
REQUIRE_HUMAN_APPROVAL=true

# Safety protocols
ENABLE_ACCOUNT_WARMING=true
ENABLE_SAFETY_DELAYS=true
ENABLE_ANOMALY_DETECTION=true
ENABLE_EMERGENCY_STOP=true

# ===================================
# BACKUP & RECOVERY
# ===================================

# Backup settings
ENABLE_AUTO_BACKUP=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30

# Recovery settings
ENABLE_AUTO_RECOVERY=true
RECOVERY_TIMEOUT_MINUTES=5

# ===================================
# NOTES
# ===================================

# This configuration enables:
# 1. Full content generation using Hugging Face API
# 2. Telegram bot integration for notifications and control
# 3. Browser assistant for manual posting assistance
# 4. Compliance monitoring and safety protocols
# 5. Advanced analytics and performance monitoring
# 6. Development tools and debugging features

# Missing X/Twitter API access means:
# - No direct posting to X/Twitter (manual posting required)
# - No real-time X/Twitter data fetching
# - No automated engagement actions
# - Content generation and analysis still fully functional

# The platform operates in "Content Creation Mode" which is:
# - Fully compliant with all terms of service
# - Focused on high-quality content generation
# - Provides manual posting assistance
# - Maintains all advanced features for content optimization
