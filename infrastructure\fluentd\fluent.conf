# Fluentd Configuration for Enterprise Log Aggregation
# Collects, processes, and forwards logs from all services

<system>
  log_level info
</system>

# Input sources
<source>
  @type forward
  port 24224
  bind 0.0.0.0
</source>

<source>
  @type tail
  path /var/log/fluentd/telegram-bot/*.log
  pos_file /var/log/fluentd/telegram-bot.log.pos
  tag telegram-bot
  format json
  time_key timestamp
  time_format %Y-%m-%dT%H:%M:%S.%LZ
</source>

<source>
  @type tail
  path /var/log/fluentd/backend/*.log
  pos_file /var/log/fluentd/backend.log.pos
  tag backend
  format json
  time_key timestamp
  time_format %Y-%m-%dT%H:%M:%S.%LZ
</source>

<source>
  @type tail
  path /var/log/fluentd/llm-service/*.log
  pos_file /var/log/fluentd/llm-service.log.pos
  tag llm-service
  format json
  time_key timestamp
  time_format %Y-%m-%dT%H:%M:%S.%LZ
</source>

# Filters for log processing
<filter **>
  @type record_transformer
  <record>
    hostname "#{Socket.gethostname}"
    environment "#{ENV['NODE_ENV'] || 'production'}"
  </record>
</filter>

<filter telegram-bot>
  @type record_transformer
  <record>
    service telegram-bot
    component webhook
  </record>
</filter>

<filter backend>
  @type record_transformer
  <record>
    service backend
    component api
  </record>
</filter>

<filter llm-service>
  @type record_transformer
  <record>
    service llm-service
    component ai
  </record>
</filter>

# Error log parsing
<filter **>
  @type grep
  <regexp>
    key level
    pattern ^(error|ERROR|fatal|FATAL)$
  </regexp>
  tag error.${tag}
</filter>

# Output to Elasticsearch
<match **>
  @type elasticsearch
  host elasticsearch
  port 9200
  index_name enterprise-logs
  type_name _doc
  logstash_format true
  logstash_prefix enterprise
  logstash_dateformat %Y.%m.%d
  include_tag_key true
  tag_key @log_name
  flush_interval 10s
  reload_connections false
  reconnect_on_error true
  reload_on_failure true
</match>

# Backup output to file
<match **>
  @type copy
  <store>
    @type file
    path /var/log/fluentd/backup/enterprise.%Y%m%d
    append true
    time_slice_format %Y%m%d
    time_slice_wait 10m
    time_format %Y%m%dT%H%M%S%z
    buffer_type file
    buffer_path /var/log/fluentd/backup/buffer
    flush_interval 60s
  </store>
</match>
