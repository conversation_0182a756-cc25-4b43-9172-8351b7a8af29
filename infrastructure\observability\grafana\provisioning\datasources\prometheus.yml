apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.45.0
      cacheLevel: 'High'
      disableRecordingRules: false
      incrementalQueryOverlapWindow: 10m
      queryTimeout: 60s
      timeInterval: 15s
    secureJsonData: {}

  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: 'loki'
        tags: ['job', 'instance', 'pod', 'namespace']
        mappedTags: [
          {
            key: 'service.name',
            value: 'service'
          }
        ]
        mapTagNamesEnabled: false
        spanStartTimeShift: '1h'
        spanEndTimeShift: '1h'
        filterByTraceID: false
        filterBySpanID: false
      tracesToMetrics:
        datasourceUid: 'prometheus'
        tags: [
          {
            key: 'service.name',
            value: 'service'
          },
          {
            key: 'job'
          }
        ]
        queries: [
          {
            name: 'Sample query',
            query: 'sum(rate(traces_spanmetrics_latency_bucket{$$__tags}[5m]))'
          }
        ]
      nodeGraph:
        enabled: true
      search:
        hide: false
      spanBar:
        type: 'Tag'
        tag: 'http.path'
    secureJsonData: {}
