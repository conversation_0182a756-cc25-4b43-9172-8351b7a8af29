
# X/Twitter Automation Platform - Complete Production Environment
NODE_ENV=production
PORT=3000
LOG_LEVEL=info
ENABLE_DETAILED_LOGGING=true

# Database Configuration (PostgreSQL for enterprise deployment)
DATABASE_URL=postgresql://x_marketing_user:secure_password_123@localhost:5432/x_marketing_platform
DATABASE_POOL_MIN=5
DATABASE_POOL_MAX=20
DATABASE_POOL_IDLE_TIMEOUT=30000
DATABASE_POOL_ACQUIRE_TIMEOUT=60000

# Redis Configuration (Memory-based for local deployment)
REDIS_URL=memory://localhost:6379
REDIS_TTL_DEFAULT=3600
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000

# Security Configuration
JWT_SECRET=ScriptAI_Production_JWT_Secret_Key_2024_Enterprise_Grade_Security
JWT_EXPIRES_IN=24h
ENCRYPTION_KEY=ScriptAI_AES256_Production_Encryption_Key_32_Characters_2024
BOT_JWT_SECRET=ScriptAI_Bot_JWT_Production_Secret_Key_2024_Enterprise
BCRYPT_ROUNDS=12

# External API Configuration
TELEGRAM_BOT_TOKEN=**********************************************
HUGGING_FACE_API_KEY=hf_1234567890abcdefghijklmnopqrstuvwxyz

# Real-Time Sync Configuration - FULL ENTERPRISE
ENABLE_REAL_TIME_SYNC=true
REAL_TIME_SYNC_LOG_LEVEL=info
ACCOUNT_SYNC_INTERVAL_SECONDS=30
ACCOUNT_SYNC_BATCH_SIZE=10
ACCOUNT_SYNC_RETRY_ATTEMPTS=3
ACCOUNT_SYNC_TIMEOUT=30000

# Analytics Collection - ENTERPRISE GRADE
ANALYTICS_COLLECTION_ENABLED=true
ANALYTICS_BUFFER_SIZE=1000
ANALYTICS_FLUSH_INTERVAL_SECONDS=10
ANALYTICS_RATE_LIMIT_PER_MINUTE=300

# Campaign Tracking - FULL FEATURED
CAMPAIGN_TRACKING_ENABLED=true
CAMPAIGN_TRACKING_INTERVAL_SECONDS=300
CAMPAIGN_ANALYTICS_INTERVAL_SECONDS=900

# WebSocket Configuration - ENTERPRISE
WEBSOCKET_ENABLED=true
WEBSOCKET_MAX_CONNECTIONS=1000
WEBSOCKET_MESSAGE_QUEUE_SIZE=100
WEBSOCKET_BROADCAST_INTERVAL_SECONDS=30
WEBSOCKET_PING_INTERVAL=25000
WEBSOCKET_PING_TIMEOUT=60000

# Data Integrity - ENTERPRISE COMPLIANCE
DATA_INTEGRITY_ENABLED=true
DATA_VALIDATION_INTERVAL_SECONDS=300
DATA_RETENTION_CHECK_INTERVAL_SECONDS=3600
DATA_QUALITY_THRESHOLD=0.8

# Anti-Detection Configuration - ADVANCED
ANTI_DETECTION_ENABLED=true
PROXY_ROTATION_ENABLED=true
FINGERPRINT_ROTATION_ENABLED=true
BEHAVIOR_SIMULATION_ENABLED=true
DETECTION_EVASION_LEVEL=high

# Performance Thresholds - ENTERPRISE
MIN_ENGAGEMENT_RATE=0.02
MIN_QUALITY_SCORE=0.7
MAX_RISK_SCORE=0.3
MAX_ACTIONS_PER_HOUR=100

# Rate Limiting - PRODUCTION GRADE
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BOT_RATE_LIMIT_PER_MINUTE=60

# CORS Configuration
FRONTEND_URL=http://localhost:3001
ALLOWED_ORIGINS=http://localhost:3001,https://yourdomain.com

# Monitoring and Health - COMPREHENSIVE
HEALTH_CHECK_ENABLED=true
METRICS_COLLECTION_ENABLED=true
PERFORMANCE_MONITORING_ENABLED=true

# Bot Configuration - ENTERPRISE
BOT_DETAILED_LOGGING=true
BOT_WEBHOOK_SECRET=ScriptAI_Webhook_Production_Secret_2024_Enterprise

# Build Configuration
BUILD_VERSION=1.0.0-enterprise
BUILD_DATE=2025-07-22T12:03:38.601Z
