# Enterprise HAProxy Configuration
# Load balancing and high availability for all services

global
    daemon
    log stdout local0 info
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin
    stats timeout 30s
    user haproxy
    group haproxy

    # SSL/TLS configuration
    ssl-default-bind-ciphers ECDHE+AESGCM:ECDHE+CHACHA20:RSA+AESGCM:RSA+SHA256:!aNULL:!MD5:!DSS
    ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets

defaults
    mode http
    log global
    option httplog
    option dontlognull
    option log-health-checks
    option forwardfor
    option http-server-close
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    timeout http-request 10s
    timeout http-keep-alive 2s
    timeout check 10s
    retries 3
    
    # Error pages
    errorfile 400 /etc/haproxy/errors/400.http
    errorfile 403 /etc/haproxy/errors/403.http
    errorfile 408 /etc/haproxy/errors/408.http
    errorfile 500 /etc/haproxy/errors/500.http
    errorfile 502 /etc/haproxy/errors/502.http
    errorfile 503 /etc/haproxy/errors/503.http
    errorfile 504 /etc/haproxy/errors/504.http

# Statistics page
frontend stats
    bind *:8404
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
    stats show-legends
    stats show-node

# Main frontend
frontend main
    bind *:8080
    
    # Security headers
    http-response set-header X-Frame-Options DENY
    http-response set-header X-Content-Type-Options nosniff
    http-response set-header X-XSS-Protection "1; mode=block"
    http-response set-header Strict-Transport-Security "max-age=********; includeSubDomains"
    
    # Rate limiting
    stick-table type ip size 100k expire 30s store http_req_rate(10s)
    http-request track-sc0 src
    http-request reject if { sc_http_req_rate(0) gt 20 }
    
    # ACLs for routing
    acl is_webhook path_beg /webhook
    acl is_api path_beg /api
    acl is_llm path_beg /llm
    acl is_health path_beg /health
    acl is_metrics path_beg /metrics
    
    # Route to appropriate backends
    use_backend webhook_backend if is_webhook
    use_backend api_backend if is_api
    use_backend llm_backend if is_llm
    use_backend health_backend if is_health
    use_backend metrics_backend if is_metrics
    
    # Default backend
    default_backend api_backend

# Webhook backend (Telegram Bot)
backend webhook_backend
    balance roundrobin
    option httpchk GET /health
    http-check expect status 200
    
    # Telegram Bot servers
    server telegram-bot-1 telegram-bot:3002 check inter 5s rise 2 fall 3 weight 100
    server telegram-bot-2 telegram-bot:3002 check inter 5s rise 2 fall 3 weight 100 backup

# API backend (Backend Service)
backend api_backend
    balance roundrobin
    option httpchk GET /api/health
    http-check expect status 200
    
    # Backend API servers
    server backend-1 backend:3001 check inter 10s rise 2 fall 3 weight 100
    server backend-2 backend:3001 check inter 10s rise 2 fall 3 weight 100 backup

# LLM backend (LLM Service)
backend llm_backend
    balance roundrobin
    option httpchk GET /health
    http-check expect status 200
    timeout server 120s
    
    # LLM service servers
    server llm-service-1 llm-service:3003 check inter 15s rise 2 fall 3 weight 100
    server llm-service-2 llm-service:3003 check inter 15s rise 2 fall 3 weight 100 backup

# Health check backend
backend health_backend
    balance roundrobin
    option httpchk GET /health
    http-check expect status 200
    
    # Health check endpoints
    server telegram-bot telegram-bot:3002 check inter 5s rise 2 fall 3 weight 50
    server backend backend:3001 check inter 5s rise 2 fall 3 weight 50

# Metrics backend
backend metrics_backend
    balance roundrobin
    option httpchk GET /metrics
    http-check expect status 200
    
    # Metrics endpoints
    server telegram-bot-metrics telegram-bot:9091 check inter 10s rise 2 fall 3 weight 33
    server backend-metrics backend:9092 check inter 10s rise 2 fall 3 weight 33
    server llm-service-metrics llm-service:9093 check inter 10s rise 2 fall 3 weight 33

# Kong API Gateway backend (if needed)
backend kong_backend
    balance roundrobin
    option httpchk GET /status
    http-check expect status 200
    
    # Kong servers
    server kong-1 kong:8000 check inter 10s rise 2 fall 3 weight 100
    server kong-2 kong:8000 check inter 10s rise 2 fall 3 weight 100 backup

# Consul backend
backend consul_backend
    balance roundrobin
    option httpchk GET /v1/status/leader
    http-check expect status 200
    
    # Consul servers
    server consul-1 consul:8500 check inter 10s rise 2 fall 3 weight 100

# Prometheus backend
backend prometheus_backend
    balance roundrobin
    option httpchk GET /-/healthy
    http-check expect status 200
    
    # Prometheus servers
    server prometheus-1 prometheus:9090 check inter 15s rise 2 fall 3 weight 100

# Grafana backend
backend grafana_backend
    balance roundrobin
    option httpchk GET /api/health
    http-check expect status 200
    
    # Grafana servers
    server grafana-1 grafana:3000 check inter 15s rise 2 fall 3 weight 100

# Jaeger backend
backend jaeger_backend
    balance roundrobin
    option httpchk GET /
    http-check expect status 200
    
    # Jaeger servers
    server jaeger-1 jaeger:16686 check inter 15s rise 2 fall 3 weight 100

# Kafka UI backend
backend kafka_ui_backend
    balance roundrobin
    option httpchk GET /
    http-check expect status 200
    
    # Kafka UI servers
    server kafka-ui-1 kafka-ui:8080 check inter 15s rise 2 fall 3 weight 100

# Elasticsearch backend
backend elasticsearch_backend
    balance roundrobin
    option httpchk GET /_cluster/health
    http-check expect status 200
    
    # Elasticsearch servers
    server elasticsearch-1 elasticsearch:9200 check inter 20s rise 2 fall 3 weight 100

# Kibana backend
backend kibana_backend
    balance roundrobin
    option httpchk GET /api/status
    http-check expect status 200
    
    # Kibana servers
    server kibana-1 kibana:5601 check inter 20s rise 2 fall 3 weight 100
