/**
 * Twikit Health Manager Tests - Task 32
 * 
 * Basic integration tests for the TwikitHealthManager service.
 * Validates core functionality including health checks, alerting, and recovery.
 */

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { 
  TwikitHealthManager, 
  HealthStatus, 
  AlertSeverity, 
  AlertChannel,
  EscalationLevel 
} from '../twikitHealthManager';

describe('TwikitHealthManager Integration Tests', () => {
  let healthManager: TwikitHealthManager;

  beforeEach(async () => {
    // Create health manager instance with test configuration
    healthManager = TwikitHealthManager.getInstance({
      enabled: true,
      globalCheckInterval: 5000, // 5 seconds for testing
      maxConcurrentChecks: 5,
      defaultTimeout: 10000,
      defaultRetries: 2,
      alerting: {
        enabled: true,
        aggregationWindow: 30000,
        deduplicationWindow: 60000,
        maxAlertsPerHour: 10,
        suppressDuringMaintenance: true,
        channels: {
          email: {
            enabled: false, // Disable for testing
            smtpHost: 'localhost',
            smtpPort: 587,
            username: '',
            password: '',
            from: '<EMAIL>',
            defaultRecipients: []
          },
          slack: {
            enabled: false, // Disable for testing
            webhookUrl: '',
            channel: '#test',
            username: 'Test Bot',
            iconEmoji: ':test:'
          },
          pagerduty: {
            enabled: false, // Disable for testing
            integrationKey: '',
            apiUrl: 'https://events.pagerduty.com/v2/enqueue'
          },
          webhook: {
            enabled: false, // Disable for testing
            urls: [],
            headers: {}
          }
        }
      },
      escalation: {
        enabled: true,
        defaultEscalationDelay: 1, // 1 minute for testing
        maxEscalationLevel: EscalationLevel.L2_ENGINEERING,
        autoAcknowledgeAfter: 5, // 5 minutes for testing
        autoResolveAfter: 10 // 10 minutes for testing
      },
      recovery: {
        enabled: true,
        autoRecoveryEnabled: true,
        maxRecoveryAttempts: 2,
        recoveryTimeout: 30000, // 30 seconds for testing
        requireApprovalForCritical: false // Disable for testing
      },
      monitoring: {
        retentionPeriod: 3600000, // 1 hour for testing
        metricsInterval: 10000, // 10 seconds for testing
        anomalyDetection: false, // Disable for testing
        predictiveMonitoring: false, // Disable for testing
        dashboardIntegration: true
      }
    });
  });

  afterEach(async () => {
    // Clean up after each test
    if (healthManager) {
      // Clear all health checks
      const healthChecks = healthManager.getAllHealthChecks();
      for (const healthCheck of healthChecks) {
        healthManager.unregisterHealthCheck(healthCheck.id);
      }
    }
  });

  // ============================================================================
  // INITIALIZATION TESTS
  // ============================================================================

  describe('Initialization', () => {
    test('should initialize health manager successfully', async () => {
      await healthManager.initializeHealthManager();
      
      const config = healthManager.getPublicConfig();
      expect(config.enabled).toBe(true);
      expect(config.globalCheckInterval).toBe(5000);
      
      // Should have default health checks registered
      const healthChecks = healthManager.getAllHealthChecks();
      expect(healthChecks.length).toBeGreaterThan(0);
      
      // Should have session manager health check
      const sessionHealthCheck = healthChecks.find(hc => hc.id === 'session-manager-health');
      expect(sessionHealthCheck).toBeDefined();
      expect(sessionHealthCheck?.enabled).toBe(true);
    });

    test('should get system health status', async () => {
      await healthManager.initializeHealthManager();
      
      const healthStatus = healthManager.getSystemHealthStatus();
      expect(healthStatus).toBeDefined();
      expect(healthStatus.status).toBeDefined();
      expect(healthStatus.summary).toBeDefined();
      expect(healthStatus.details).toBeDefined();
      expect(healthStatus.details.totalChecks).toBeGreaterThanOrEqual(0);
      expect(healthStatus.lastUpdate).toBeInstanceOf(Date);
    });
  });

  // ============================================================================
  // HEALTH CHECK TESTS
  // ============================================================================

  describe('Health Check Management', () => {
    beforeEach(async () => {
      await healthManager.initializeHealthManager();
    });

    test('should register custom health check', () => {
      const customHealthCheck = {
        id: 'test-service-health',
        name: 'Test Service Health',
        description: 'Test service health check',
        service: 'TestService',
        category: 'application' as const,
        interval: 30000,
        timeout: 10000,
        retries: 2,
        enabled: true,
        dependencies: [],
        thresholds: {
          warning: { responseTime: 1000 },
          critical: { responseTime: 5000 }
        },
        checkFunction: async () => ({
          status: HealthStatus.HEALTHY,
          message: 'Test service is healthy',
          timestamp: new Date(),
          responseTime: 150,
          metrics: { testMetric: 100 }
        })
      };

      healthManager.registerHealthCheck(customHealthCheck);
      
      const retrievedCheck = healthManager.getHealthCheck('test-service-health');
      expect(retrievedCheck).toBeDefined();
      expect(retrievedCheck?.name).toBe('Test Service Health');
      expect(retrievedCheck?.service).toBe('TestService');
    });

    test('should execute health check successfully', async () => {
      // Register a test health check
      healthManager.registerHealthCheck({
        id: 'test-health-check',
        name: 'Test Health Check',
        description: 'A test health check',
        service: 'TestService',
        category: 'application',
        interval: 60000,
        timeout: 5000,
        retries: 1,
        enabled: true,
        dependencies: [],
        thresholds: {
          warning: { responseTime: 1000 },
          critical: { responseTime: 3000 }
        },
        checkFunction: async () => ({
          status: HealthStatus.HEALTHY,
          message: 'Test check passed',
          timestamp: new Date(),
          responseTime: 100
        })
      });

      const result = await healthManager.executeHealthCheck('test-health-check');
      
      expect(result).toBeDefined();
      expect(result.status).toBe(HealthStatus.HEALTHY);
      expect(result.message).toBe('Test check passed');
      expect(result.responseTime).toBe(100);
      expect(result.timestamp).toBeInstanceOf(Date);
    });

    test('should handle health check failure', async () => {
      // Register a failing health check
      healthManager.registerHealthCheck({
        id: 'failing-health-check',
        name: 'Failing Health Check',
        description: 'A health check that always fails',
        service: 'FailingService',
        category: 'application',
        interval: 60000,
        timeout: 5000,
        retries: 1,
        enabled: true,
        dependencies: [],
        thresholds: {
          warning: { responseTime: 1000 },
          critical: { responseTime: 3000 }
        },
        checkFunction: async () => {
          throw new Error('Health check failed');
        }
      });

      const result = await healthManager.executeHealthCheck('failing-health-check');
      
      expect(result).toBeDefined();
      expect(result.status).toBe(HealthStatus.CRITICAL);
      expect(result.message).toContain('Health check execution failed');
      expect(result.responseTime).toBeGreaterThan(0);
    });

    test('should get health checks by service', async () => {
      // Register multiple health checks for the same service
      healthManager.registerHealthCheck({
        id: 'service-a-check-1',
        name: 'Service A Check 1',
        description: 'First check for Service A',
        service: 'ServiceA',
        category: 'application',
        interval: 60000,
        timeout: 5000,
        retries: 1,
        enabled: true,
        dependencies: [],
        thresholds: { warning: {}, critical: {} },
        checkFunction: async () => ({
          status: HealthStatus.HEALTHY,
          message: 'OK',
          timestamp: new Date(),
          responseTime: 100
        })
      });

      healthManager.registerHealthCheck({
        id: 'service-a-check-2',
        name: 'Service A Check 2',
        description: 'Second check for Service A',
        service: 'ServiceA',
        category: 'application',
        interval: 60000,
        timeout: 5000,
        retries: 1,
        enabled: true,
        dependencies: [],
        thresholds: { warning: {}, critical: {} },
        checkFunction: async () => ({
          status: HealthStatus.HEALTHY,
          message: 'OK',
          timestamp: new Date(),
          responseTime: 100
        })
      });

      const serviceAChecks = healthManager.getHealthChecksByService('ServiceA');
      expect(serviceAChecks).toHaveLength(2);
      expect(serviceAChecks.every(check => check.service === 'ServiceA')).toBe(true);
    });
  });

  // ============================================================================
  // ALERT MANAGEMENT TESTS
  // ============================================================================

  describe('Alert Management', () => {
    beforeEach(async () => {
      await healthManager.initializeHealthManager();
    });

    test('should get active alerts', () => {
      const activeAlerts = healthManager.getActiveAlerts();
      expect(Array.isArray(activeAlerts)).toBe(true);
    });

    test('should acknowledge alert', async () => {
      // This test would require creating an actual alert first
      // For now, we'll test the method exists and handles invalid IDs gracefully
      const result = await healthManager.acknowledgeAlert('non-existent-alert', 'test-user');
      expect(result).toBe(false);
    });

    test('should resolve alert', async () => {
      // This test would require creating an actual alert first
      // For now, we'll test the method exists and handles invalid IDs gracefully
      const result = await healthManager.resolveAlert('non-existent-alert', 'Test resolution');
      expect(result).toBe(false);
    });

    test('should get all alerts with filters', () => {
      const allAlerts = healthManager.getAllAlerts();
      expect(Array.isArray(allAlerts)).toBe(true);

      const filteredAlerts = healthManager.getAllAlerts({
        status: 'active',
        severity: AlertSeverity.CRITICAL,
        limit: 5
      });
      expect(Array.isArray(filteredAlerts)).toBe(true);
      expect(filteredAlerts.length).toBeLessThanOrEqual(5);
    });
  });

  // ============================================================================
  // CONFIGURATION TESTS
  // ============================================================================

  describe('Configuration Management', () => {
    beforeEach(async () => {
      await healthManager.initializeHealthManager();
    });

    test('should get public configuration', () => {
      const config = healthManager.getPublicConfig();
      
      expect(config).toBeDefined();
      expect(config.enabled).toBe(true);
      expect(config.globalCheckInterval).toBe(5000);
      expect(config.alerting).toBeDefined();
      expect(config.escalation).toBeDefined();
      expect(config.recovery).toBeDefined();
      expect(config.monitoring).toBeDefined();
    });

    test('should get health manager statistics', () => {
      const stats = healthManager.getHealthManagerStats();
      
      expect(stats).toBeDefined();
      expect(stats.healthChecks).toBeDefined();
      expect(stats.alerts).toBeDefined();
      expect(stats.uptime).toBeGreaterThanOrEqual(0);
      expect(stats.lastHealthCheck).toBeInstanceOf(Date);
      
      expect(typeof stats.healthChecks.totalChecks).toBe('number');
      expect(typeof stats.healthChecks.successfulChecks).toBe('number');
      expect(typeof stats.healthChecks.failedChecks).toBe('number');
      expect(typeof stats.healthChecks.averageResponseTime).toBe('number');
    });

    test('should handle maintenance mode', () => {
      expect(healthManager.isInMaintenanceMode()).toBe(false);
      
      healthManager.enableMaintenanceMode('Testing maintenance mode');
      expect(healthManager.isInMaintenanceMode()).toBe(true);
      
      healthManager.disableMaintenanceMode();
      expect(healthManager.isInMaintenanceMode()).toBe(false);
    });
  });

  // ============================================================================
  // HEALTH CHECK RESULTS TESTS
  // ============================================================================

  describe('Health Check Results', () => {
    beforeEach(async () => {
      await healthManager.initializeHealthManager();
    });

    test('should get health check results', () => {
      const results = healthManager.getHealthCheckResults();
      expect(results).toBeInstanceOf(Map);
    });

    test('should update health check configuration', () => {
      // First register a health check
      healthManager.registerHealthCheck({
        id: 'updateable-check',
        name: 'Updateable Check',
        description: 'A check that can be updated',
        service: 'UpdateableService',
        category: 'application',
        interval: 60000,
        timeout: 5000,
        retries: 1,
        enabled: true,
        dependencies: [],
        thresholds: { warning: {}, critical: {} },
        checkFunction: async () => ({
          status: HealthStatus.HEALTHY,
          message: 'OK',
          timestamp: new Date(),
          responseTime: 100
        })
      });

      // Update the health check
      const updated = healthManager.updateHealthCheck('updateable-check', {
        interval: 30000,
        enabled: false
      });

      expect(updated).toBe(true);

      const updatedCheck = healthManager.getHealthCheck('updateable-check');
      expect(updatedCheck?.interval).toBe(30000);
      expect(updatedCheck?.enabled).toBe(false);
    });

    test('should enable and disable health checks', () => {
      // First register a health check
      healthManager.registerHealthCheck({
        id: 'toggleable-check',
        name: 'Toggleable Check',
        description: 'A check that can be toggled',
        service: 'ToggleableService',
        category: 'application',
        interval: 60000,
        timeout: 5000,
        retries: 1,
        enabled: true,
        dependencies: [],
        thresholds: { warning: {}, critical: {} },
        checkFunction: async () => ({
          status: HealthStatus.HEALTHY,
          message: 'OK',
          timestamp: new Date(),
          responseTime: 100
        })
      });

      // Disable the health check
      const disabled = healthManager.setHealthCheckEnabled('toggleable-check', false);
      expect(disabled).toBe(true);

      const disabledCheck = healthManager.getHealthCheck('toggleable-check');
      expect(disabledCheck?.enabled).toBe(false);

      // Enable the health check
      const enabled = healthManager.setHealthCheckEnabled('toggleable-check', true);
      expect(enabled).toBe(true);

      const enabledCheck = healthManager.getHealthCheck('toggleable-check');
      expect(enabledCheck?.enabled).toBe(true);
    });
  });
});
