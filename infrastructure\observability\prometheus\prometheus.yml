global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'x-marketing-platform'
    environment: 'enterprise'

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 15s

  # Application Services
  - job_name: 'backend'
    static_configs:
      - targets: ['backend:9094']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s
    honor_labels: true

  - job_name: 'telegram-bot'
    static_configs:
      - targets: ['telegram-bot:9096']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s
    honor_labels: true

  - job_name: 'llm-service'
    static_configs:
      - targets: ['llm-service:9095']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s
    honor_labels: true

  # Infrastructure Services
  - job_name: 'consul'
    static_configs:
      - targets: ['consul:8500']
    metrics_path: /v1/agent/metrics
    params:
      format: ['prometheus']
    scrape_interval: 15s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9101']
    metrics_path: /metrics
    scrape_interval: 15s

  # System Metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    metrics_path: /metrics
    scrape_interval: 15s

  # API Gateway
  - job_name: 'kong'
    static_configs:
      - targets: ['kong:8001']
    metrics_path: /metrics
    scrape_interval: 15s

  # Observability Stack
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'jaeger'
    static_configs:
      - targets: ['jaeger:14269']
    metrics_path: /metrics
    scrape_interval: 30s

  # Service Discovery via Consul
  - job_name: 'consul-services'
    consul_sd_configs:
      - server: 'consul:8500'
        datacenter: 'dc1'
    relabel_configs:
      - source_labels: [__meta_consul_service]
        target_label: job
      - source_labels: [__meta_consul_service_id]
        target_label: instance
      - source_labels: [__meta_consul_tags]
        target_label: consul_tags
        regex: '(.+)'
      - source_labels: [__meta_consul_service_metadata_metrics_path]
        target_label: __metrics_path__
        regex: '(.+)'
      - source_labels: [__meta_consul_service_metadata_metrics_port]
        target_label: __address__
        regex: '(.+)'
        replacement: '${1}:${__meta_consul_service_metadata_metrics_port}'
