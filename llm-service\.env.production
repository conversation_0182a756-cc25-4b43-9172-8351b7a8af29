# LLM Service Configuration - PRODUCTION WITH REAL API KEYS
FLASK_ENV=development
FLASK_DEBUG=1
PORT=3003

# API Keys - REAL PRODUCTION KEYS
HUGGINGFACE_API_KEY=*************************************
GEMINI_API_KEY=AIzaSyD6S37q8FVrtsPzsY-0VmxNRfI7Ez9qJFs

# Optional API Keys
OPENAI_API_KEY=demo-openai-key
ANTHROPIC_API_KEY=demo-anthropic-key

# Model Configuration
DEFAULT_TEXT_MODEL=microsoft/DialoGPT-large
DEFAULT_CONTENT_MODEL=gpt2
DEFAULT_SENTIMENT_MODEL=cardiffnlp/twitter-roberta-base-sentiment-latest

# Gemini 2.5 Enterprise Configuration (July 2025 - Actual Available Models)
GEMINI_PRIMARY_MODEL=gemini-2.5-pro
GEMINI_SECONDARY_MODEL=gemini-2.5-flash
GEMINI_REASONING_MODEL=gemini-2.5-flash
GEMINI_LEGACY_FALLBACK=gemini-2.0-flash-exp
GEMINI_MAX_TOKENS=1048576
GEMINI_TEMPERATURE=0.7
GEMINI_TOP_P=0.9
GEMINI_TOP_K=40

# Multimodal Configuration
ENABLE_MULTIMODAL_PROCESSING=true
ENABLE_AUDIO_GENERATION=true
ENABLE_VIDEO_ANALYSIS=true
ENABLE_DEEP_THINK_MODE=true
MULTIMODAL_CONTEXT_FUSION=true
CROSS_MODAL_OPTIMIZATION=true

# Enterprise Rate Limiting Configuration (2.5 Models)
GEMINI_PRO_2_5_RPM_LIMIT=10
GEMINI_PRO_2_5_RPD_LIMIT=100
GEMINI_FLASH_2_5_RPM_LIMIT=50
GEMINI_FLASH_2_5_RPD_LIMIT=2000
GEMINI_DEEP_THINK_RPM_LIMIT=5
GEMINI_DEEP_THINK_RPD_LIMIT=50
GEMINI_TPM_LIMIT=2000000
RATE_LIMIT_WINDOW_SIZE=60
QUEUE_MAX_SIZE=2000
PRIORITY_QUEUE_ENABLED=true
INTELLIGENT_MODEL_ROUTING=true
DYNAMIC_LOAD_BALANCING=true

# Service Configuration
ENABLE_CACHING=true
MAX_TOKENS=2048
TEMPERATURE=0.7
MAX_REQUESTS_PER_MINUTE=60

# Backend Integration
BACKEND_URL=http://localhost:3001
TELEGRAM_BOT_URL=http://localhost:3002

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Monitoring & Analytics
ENABLE_MONITORING=true
METRICS_COLLECTION_INTERVAL=30
PERFORMANCE_TRACKING=true
USAGE_ANALYTICS=true
COST_TRACKING=true

# Fallback Configuration
ENABLE_HUGGINGFACE_FALLBACK=true
FALLBACK_THRESHOLD_ERRORS=5
FALLBACK_COOLDOWN_MINUTES=10
CIRCUIT_BREAKER_ENABLED=true

# Enterprise Function Calling Configuration
ENABLE_FUNCTION_CALLING=true
ENABLE_STRUCTURED_OUTPUTS=true
MAX_FUNCTION_CALLS_PER_REQUEST=20
FUNCTION_CALL_TIMEOUT=60
FUNCTION_CALL_CHAINING=true
PARALLEL_FUNCTION_EXECUTION=true

# Advanced Context Management
CONTEXT_WINDOW_MANAGEMENT=true
CONTEXT_COMPRESSION_ENABLED=true
MAX_CONTEXT_HISTORY=100
MULTIMODAL_CONTEXT_FUSION=true
CONTEXT_OPTIMIZATION=true
SEMANTIC_CONTEXT_CHUNKING=true

# Enterprise Orchestration
ENABLE_DEEP_THINK_ORCHESTRATION=true
ENABLE_MULTIMODAL_ORCHESTRATION=true
ENABLE_CROSS_PLATFORM_OPTIMIZATION=true
ENABLE_REAL_TIME_ADAPTATION=true
ENABLE_PREDICTIVE_ANALYTICS=true
CAMPAIGN_COMPLEXITY_ANALYSIS=true

# Logging
LOG_LEVEL=debug
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_LOGGING=true
ENABLE_ERROR_TRACKING=true
