{"name": "user-management-service", "version": "1.0.0", "description": "Enterprise User Management Microservice for X Marketing Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "start:production": "NODE_ENV=production node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@opentelemetry/api": "^1.8.0", "@opentelemetry/auto-instrumentations-node": "^0.46.1", "@opentelemetry/exporter-jaeger": "^1.24.1", "@opentelemetry/instrumentation-express": "^0.39.0", "@opentelemetry/instrumentation-http": "^0.51.1", "@opentelemetry/resources": "^1.24.1", "@opentelemetry/sdk-node": "^0.51.1", "@opentelemetry/semantic-conventions": "^1.24.1", "@prisma/client": "^6.11.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.0.1", "helmet": "^7.2.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "morgan": "^1.10.0", "prom-client": "^15.0.0", "speakeasy": "^2.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.25.75"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/module-alias": "^2.0.4", "@types/morgan": "^1.9.10", "@types/node": "^20.19.4", "@types/speakeasy": "^2.0.10", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.17.0", "jest": "^29.7.0", "module-alias": "^2.2.3", "nodemon": "^3.0.2", "prisma": "^6.11.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "engines": {"node": ">=20.0.0"}, "keywords": ["microservice", "user-management", "authentication", "enterprise", "kafka", "consul", "typescript"], "author": "X Marketing Platform Team", "license": "PROPRIETARY", "_moduleAliases": {"@": "dist", "@/config": "dist/config", "@/services": "dist/services", "@/utils": "dist/utils", "@/types": "dist/types", "@/middleware": "dist/middleware", "@/routes": "dist/routes"}}