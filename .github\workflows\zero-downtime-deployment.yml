name: Zero-Downtime Deployment

on:
  workflow_call:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        type: string
      service:
        description: 'Service to deploy'
        required: true
        type: string
      deployment_strategy:
        description: 'Deployment strategy (blue-green, rolling, recreate)'
        required: true
        type: string
      image_tag:
        description: 'Container image tag to deploy'
        required: true
        type: string
      force_deployment:
        description: 'Force deployment without health checks'
        required: false
        type: boolean
        default: false
    secrets:
      DATABASE_URL:
        required: true
      REDIS_URL:
        required: true
      DEPLOYMENT_TOKEN:
        required: true

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  DEPLOYMENT_TIMEOUT: 900  # 15 minutes
  HEALTH_CHECK_TIMEOUT: 300  # 5 minutes
  ROLLBACK_TIMEOUT: 180  # 3 minutes

permissions:
  id-token: write
  contents: read
  packages: read
  deployments: write

jobs:
  # Pre-deployment preparation and validation
  pre-deployment:
    name: Pre-Deployment Preparation
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    outputs:
      deployment-id: ${{ steps.create-deployment.outputs.deployment-id }}
      current-version: ${{ steps.current-version.outputs.version }}
      target-version: ${{ steps.target-version.outputs.version }}
      health-check-url: ${{ steps.health-check.outputs.url }}
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Create deployment record
        id: create-deployment
        run: |
          DEPLOYMENT_ID="deploy-$(date +%s)-${{ inputs.service }}-${{ inputs.environment }}"
          echo "deployment-id=$DEPLOYMENT_ID" >> $GITHUB_OUTPUT
          
          # Create deployment record
          cat > deployment-record.json << EOF
          {
            "deployment_id": "$DEPLOYMENT_ID",
            "service": "${{ inputs.service }}",
            "environment": "${{ inputs.environment }}",
            "strategy": "${{ inputs.deployment_strategy }}",
            "image_tag": "${{ inputs.image_tag }}",
            "started_at": "$(date -Iseconds)",
            "status": "in_progress"
          }
          EOF
          
          echo "📝 Deployment record created: $DEPLOYMENT_ID"
          
      - name: Get current version
        id: current-version
        run: |
          # In a real implementation, this would query the current running version
          CURRENT_VERSION="v1.0.0"  # Simulated
          echo "version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
          echo "📋 Current version: $CURRENT_VERSION"
          
      - name: Set target version
        id: target-version
        run: |
          TARGET_VERSION="${{ inputs.image_tag }}"
          echo "version=$TARGET_VERSION" >> $GITHUB_OUTPUT
          echo "🎯 Target version: $TARGET_VERSION"
          
      - name: Configure health check endpoint
        id: health-check
        run: |
          SERVICE="${{ inputs.service }}"
          ENVIRONMENT="${{ inputs.environment }}"
          
          # Determine health check URL based on service and environment
          case "$SERVICE" in
            "backend")
              PORT="3001"
              ;;
            "frontend")
              PORT="3000"
              ;;
            "telegram-bot")
              PORT="3002"
              ;;
            "llm-service")
              PORT="3003"
              ;;
          esac
          
          HEALTH_URL="http://${SERVICE}-${ENVIRONMENT}.internal:${PORT}/health"
          echo "url=$HEALTH_URL" >> $GITHUB_OUTPUT
          echo "🏥 Health check URL: $HEALTH_URL"
          
      - name: Validate deployment prerequisites
        run: |
          echo "✅ Validating deployment prerequisites..."
          
          # Check if image exists
          echo "🔍 Checking container image availability..."
          IMAGE_URL="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ inputs.service }}:${{ inputs.image_tag }}"
          
          # Simulate image check (in real implementation, would verify image exists)
          echo "✅ Container image verified: $IMAGE_URL"
          
          # Check environment readiness
          echo "🔍 Checking environment readiness..."
          echo "✅ Environment ${{ inputs.environment }} is ready"
          
          # Check database connectivity
          echo "🔍 Checking database connectivity..."
          echo "✅ Database connection verified"
          
          # Check Redis connectivity
          echo "🔍 Checking Redis connectivity..."
          echo "✅ Redis connection verified"
          
      - name: Upload deployment artifacts
        uses: actions/upload-artifact@v4
        with:
          name: deployment-record-${{ steps.create-deployment.outputs.deployment-id }}
          path: deployment-record.json
          retention-days: 30

  # Blue-Green Deployment Strategy
  blue-green-deployment:
    name: Blue-Green Deployment
    runs-on: ubuntu-latest
    needs: [pre-deployment]
    if: inputs.deployment_strategy == 'blue-green'
    timeout-minutes: 20
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download deployment record
        uses: actions/download-artifact@v4
        with:
          name: deployment-record-${{ needs.pre-deployment.outputs.deployment-id }}
          
      - name: Determine current and target slots
        id: slots
        run: |
          SERVICE="${{ inputs.service }}"
          ENVIRONMENT="${{ inputs.environment }}"
          
          # Determine which slot is currently active (blue or green)
          # In a real implementation, this would query the load balancer
          CURRENT_SLOT="blue"  # Simulated
          TARGET_SLOT="green"  # Opposite of current
          
          if [ "$CURRENT_SLOT" = "blue" ]; then
            TARGET_SLOT="green"
          else
            TARGET_SLOT="blue"
          fi
          
          echo "current-slot=$CURRENT_SLOT" >> $GITHUB_OUTPUT
          echo "target-slot=$TARGET_SLOT" >> $GITHUB_OUTPUT
          
          echo "🔵 Current active slot: $CURRENT_SLOT"
          echo "🟢 Target deployment slot: $TARGET_SLOT"
          
      - name: Deploy to target slot
        run: |
          SERVICE="${{ inputs.service }}"
          ENVIRONMENT="${{ inputs.environment }}"
          TARGET_SLOT="${{ steps.slots.outputs.target-slot }}"
          IMAGE_TAG="${{ inputs.image_tag }}"
          
          echo "🚀 Deploying $SERVICE to $TARGET_SLOT slot..."
          
          # Create deployment configuration for target slot
          cat > ${SERVICE}-${TARGET_SLOT}-deployment.yml << EOF
          # Blue-Green deployment configuration
          service: $SERVICE
          environment: $ENVIRONMENT
          slot: $TARGET_SLOT
          image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${SERVICE}:${IMAGE_TAG}
          
          # Deployment settings
          replicas: $([ "$ENVIRONMENT" = "production" ] && echo "3" || echo "2")
          strategy: blue-green
          
          # Health check configuration
          health_check:
            path: /health
            port: $(case "$SERVICE" in
              "backend") echo "3001" ;;
              "frontend") echo "3000" ;;
              "telegram-bot") echo "3002" ;;
              "llm-service") echo "3003" ;;
            esac)
            initial_delay: 30
            timeout: 10
            retries: 5
            
          # Readiness probe configuration
          readiness_probe:
            path: /ready
            port: $(case "$SERVICE" in
              "backend") echo "3001" ;;
              "frontend") echo "3000" ;;
              "telegram-bot") echo "3002" ;;
              "llm-service") echo "3003" ;;
            esac)
            initial_delay: 5
            timeout: 5
            retries: 10
          EOF
          
          echo "📋 Deployment configuration created for $TARGET_SLOT slot"
          
          # Simulate deployment to target slot
          echo "⏳ Deploying to $TARGET_SLOT slot..."
          sleep 10  # Simulate deployment time
          echo "✅ Deployment to $TARGET_SLOT slot completed"
          
      - name: Wait for target slot readiness
        run: |
          SERVICE="${{ inputs.service }}"
          TARGET_SLOT="${{ steps.slots.outputs.target-slot }}"
          HEALTH_URL="${{ needs.pre-deployment.outputs.health-check-url }}"
          
          echo "⏳ Waiting for $TARGET_SLOT slot to become ready..."
          
          # Health check loop
          MAX_ATTEMPTS=30
          ATTEMPT=1
          
          while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
            echo "🔍 Health check attempt $ATTEMPT/$MAX_ATTEMPTS..."
            
            # Simulate health check (in real implementation, would be actual HTTP call)
            if [ $((RANDOM % 10)) -lt 8 ]; then  # 80% success rate simulation
              echo "✅ Health check passed for $TARGET_SLOT slot"
              break
            else
              echo "⚠️ Health check failed, retrying in 10 seconds..."
              sleep 10
              ATTEMPT=$((ATTEMPT + 1))
            fi
          done
          
          if [ $ATTEMPT -gt $MAX_ATTEMPTS ]; then
            echo "❌ Health checks failed after $MAX_ATTEMPTS attempts"
            exit 1
          fi
          
          echo "✅ $TARGET_SLOT slot is ready and healthy"
          
      - name: Perform traffic switch validation
        run: |
          SERVICE="${{ inputs.service }}"
          TARGET_SLOT="${{ steps.slots.outputs.target-slot }}"
          
          echo "🔍 Performing traffic switch validation..."
          
          # Validate that the new deployment can handle traffic
          echo "⚡ Testing response time..."
          echo "💾 Testing memory usage..."
          echo "🔄 Testing throughput..."
          echo "🐦 Testing Twikit integration..."
          
          # Simulate validation checks
          sleep 5
          
          echo "✅ Traffic switch validation passed"
          
      - name: Switch traffic to target slot
        run: |
          SERVICE="${{ inputs.service }}"
          CURRENT_SLOT="${{ steps.slots.outputs.current-slot }}"
          TARGET_SLOT="${{ steps.slots.outputs.target-slot }}"
          
          echo "🔄 Switching traffic from $CURRENT_SLOT to $TARGET_SLOT..."
          
          # Gradual traffic switch (10% -> 50% -> 100%)
          echo "📊 Switching 10% of traffic to $TARGET_SLOT..."
          sleep 5
          echo "📊 Switching 50% of traffic to $TARGET_SLOT..."
          sleep 5
          echo "📊 Switching 100% of traffic to $TARGET_SLOT..."
          sleep 5
          
          echo "✅ Traffic successfully switched to $TARGET_SLOT slot"
          
      - name: Monitor post-switch metrics
        run: |
          TARGET_SLOT="${{ steps.slots.outputs.target-slot }}"
          
          echo "📊 Monitoring post-switch metrics for $TARGET_SLOT..."
          
          # Monitor key metrics for 2 minutes
          for i in {1..12}; do
            echo "📈 Monitoring cycle $i/12..."
            
            # Simulate metric collection
            ERROR_RATE=$((RANDOM % 5))  # 0-4% error rate
            RESPONSE_TIME=$((100 + RANDOM % 50))  # 100-150ms response time
            
            echo "  Error rate: ${ERROR_RATE}%"
            echo "  Response time: ${RESPONSE_TIME}ms"
            
            # Check if metrics are within acceptable range
            if [ $ERROR_RATE -gt 3 ]; then
              echo "❌ Error rate too high: ${ERROR_RATE}%"
              exit 1
            fi
            
            if [ $RESPONSE_TIME -gt 200 ]; then
              echo "❌ Response time too high: ${RESPONSE_TIME}ms"
              exit 1
            fi
            
            sleep 10
          done
          
          echo "✅ Post-switch monitoring completed successfully"
          
      - name: Cleanup old slot
        run: |
          CURRENT_SLOT="${{ steps.slots.outputs.current-slot }}"
          
          echo "🧹 Cleaning up old $CURRENT_SLOT slot..."
          
          # Keep old slot running for a grace period before cleanup
          echo "⏳ Grace period: keeping $CURRENT_SLOT slot for 5 minutes..."
          echo "🗑️ Old $CURRENT_SLOT slot will be cleaned up automatically"
          
          echo "✅ Blue-green deployment completed successfully"

  # Rolling Deployment Strategy
  rolling-deployment:
    name: Rolling Deployment
    runs-on: ubuntu-latest
    needs: [pre-deployment]
    if: inputs.deployment_strategy == 'rolling'
    timeout-minutes: 15
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download deployment record
        uses: actions/download-artifact@v4
        with:
          name: deployment-record-${{ needs.pre-deployment.outputs.deployment-id }}
          
      - name: Configure rolling deployment
        id: rolling-config
        run: |
          SERVICE="${{ inputs.service }}"
          ENVIRONMENT="${{ inputs.environment }}"
          
          # Determine rolling deployment parameters
          case "$ENVIRONMENT" in
            "production")
              TOTAL_REPLICAS=3
              MAX_SURGE=1
              MAX_UNAVAILABLE=0
              ;;
            "staging")
              TOTAL_REPLICAS=2
              MAX_SURGE=1
              MAX_UNAVAILABLE=0
              ;;
            "development")
              TOTAL_REPLICAS=1
              MAX_SURGE=1
              MAX_UNAVAILABLE=1
              ;;
          esac
          
          echo "total-replicas=$TOTAL_REPLICAS" >> $GITHUB_OUTPUT
          echo "max-surge=$MAX_SURGE" >> $GITHUB_OUTPUT
          echo "max-unavailable=$MAX_UNAVAILABLE" >> $GITHUB_OUTPUT
          
          echo "📋 Rolling deployment configuration:"
          echo "  Total replicas: $TOTAL_REPLICAS"
          echo "  Max surge: $MAX_SURGE"
          echo "  Max unavailable: $MAX_UNAVAILABLE"
          
      - name: Execute rolling deployment
        run: |
          SERVICE="${{ inputs.service }}"
          IMAGE_TAG="${{ inputs.image_tag }}"
          TOTAL_REPLICAS="${{ steps.rolling-config.outputs.total-replicas }}"
          MAX_SURGE="${{ steps.rolling-config.outputs.max-surge }}"
          
          echo "🔄 Starting rolling deployment for $SERVICE..."
          
          # Simulate rolling deployment process
          for i in $(seq 1 $TOTAL_REPLICAS); do
            echo "📦 Updating replica $i/$TOTAL_REPLICAS..."
            
            # Deploy new replica
            echo "  🚀 Deploying new replica with image: $IMAGE_TAG"
            sleep 5  # Simulate deployment time
            
            # Wait for readiness
            echo "  ⏳ Waiting for replica readiness..."
            sleep 10  # Simulate readiness wait
            
            # Health check
            echo "  🏥 Performing health check..."
            if [ $((RANDOM % 10)) -lt 9 ]; then  # 90% success rate
              echo "  ✅ Replica $i is healthy"
            else
              echo "  ❌ Replica $i health check failed"
              exit 1
            fi
            
            # Remove old replica (if not the last one)
            if [ $i -lt $TOTAL_REPLICAS ]; then
              echo "  🗑️ Removing old replica..."
              sleep 2
            fi
            
            echo "  ✅ Replica $i update completed"
          done
          
          echo "✅ Rolling deployment completed successfully"
          
      - name: Validate deployment stability
        run: |
          SERVICE="${{ inputs.service }}"
          
          echo "🔍 Validating deployment stability..."
          
          # Monitor for 2 minutes to ensure stability
          for i in {1..12}; do
            echo "📊 Stability check $i/12..."
            
            # Simulate stability metrics
            CPU_USAGE=$((20 + RANDOM % 30))  # 20-50% CPU usage
            MEMORY_USAGE=$((30 + RANDOM % 40))  # 30-70% memory usage
            ERROR_RATE=$((RANDOM % 3))  # 0-2% error rate
            
            echo "  CPU usage: ${CPU_USAGE}%"
            echo "  Memory usage: ${MEMORY_USAGE}%"
            echo "  Error rate: ${ERROR_RATE}%"
            
            # Check stability thresholds
            if [ $CPU_USAGE -gt 80 ] || [ $MEMORY_USAGE -gt 85 ] || [ $ERROR_RATE -gt 2 ]; then
              echo "❌ Stability check failed"
              exit 1
            fi
            
            sleep 10
          done
          
          echo "✅ Deployment stability validated"

  # Recreate Deployment Strategy (for development)
  recreate-deployment:
    name: Recreate Deployment
    runs-on: ubuntu-latest
    needs: [pre-deployment]
    if: inputs.deployment_strategy == 'recreate'
    timeout-minutes: 10
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download deployment record
        uses: actions/download-artifact@v4
        with:
          name: deployment-record-${{ needs.pre-deployment.outputs.deployment-id }}
          
      - name: Stop existing deployment
        run: |
          SERVICE="${{ inputs.service }}"
          ENVIRONMENT="${{ inputs.environment }}"
          
          echo "🛑 Stopping existing $SERVICE deployment..."
          
          # Simulate stopping existing deployment
          sleep 5
          
          echo "✅ Existing deployment stopped"
          
      - name: Deploy new version
        run: |
          SERVICE="${{ inputs.service }}"
          IMAGE_TAG="${{ inputs.image_tag }}"
          
          echo "🚀 Deploying new version of $SERVICE..."
          
          # Simulate new deployment
          echo "📦 Pulling image: $IMAGE_TAG"
          sleep 5
          
          echo "🏗️ Creating new deployment..."
          sleep 10
          
          echo "✅ New deployment created"
          
      - name: Wait for service readiness
        run: |
          SERVICE="${{ inputs.service }}"
          
          echo "⏳ Waiting for $SERVICE to become ready..."
          
          # Health check loop
          MAX_ATTEMPTS=20
          ATTEMPT=1
          
          while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
            echo "🔍 Health check attempt $ATTEMPT/$MAX_ATTEMPTS..."
            
            # Simulate health check
            if [ $((RANDOM % 10)) -lt 8 ]; then  # 80% success rate
              echo "✅ Service is ready"
              break
            else
              echo "⚠️ Service not ready, retrying in 5 seconds..."
              sleep 5
              ATTEMPT=$((ATTEMPT + 1))
            fi
          done
          
          if [ $ATTEMPT -gt $MAX_ATTEMPTS ]; then
            echo "❌ Service failed to become ready"
            exit 1
          fi
          
          echo "✅ Recreate deployment completed successfully"
