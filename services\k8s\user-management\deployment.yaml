apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-management-service
  namespace: x-marketing-platform
  labels:
    app: user-management-service
    version: v1.0.0
    component: microservice
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-management-service
  template:
    metadata:
      labels:
        app: user-management-service
        version: v1.0.0
    spec:
      containers:
      - name: user-management-service
        image: x-marketing/user-management-service:latest
        ports:
        - containerPort: 3011
          name: http
        - containerPort: 9091
          name: metrics
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3011"
        - name: HOST
          value: "0.0.0.0"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: redis-config
              key: url
        - name: KAFKA_BROKERS
          valueFrom:
            configMapKeyRef:
              name: kafka-config
              key: brokers
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: JWT_REFRESH_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: refresh-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3011
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 3011
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        emptyDir: {}
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: user-management-service
  namespace: x-marketing-platform
  labels:
    app: user-management-service
spec:
  selector:
    app: user-management-service
  ports:
  - name: http
    port: 3011
    targetPort: 3011
    protocol: TCP
  - name: metrics
    port: 9091
    targetPort: 9091
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: user-management-service
  namespace: x-marketing-platform
  labels:
    app: user-management-service
spec:
  selector:
    matchLabels:
      app: user-management-service
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
