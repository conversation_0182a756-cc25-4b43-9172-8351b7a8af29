version: '3.8'

services:
  # =============================================================================
  # SERVICE DISCOVERY & CONFIGURATION
  # =============================================================================
  consul:
    image: hashicorp/consul:1.15
    hostname: consul
    container_name: consul-enterprise
    command: consul agent -config-file=/consul/config/consul.json
    environment:
      CONSUL_BIND_INTERFACE: eth0
      CONSUL_CLIENT_INTERFACE: eth0
    ports:
      - "8500:8500"
      - "8600:8600/udp"
    volumes:
      - ./infrastructure/consul/consul.json:/consul/config/consul.json:ro
      - consul-data:/consul/data
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "consul", "members"]
      interval: 10s
      timeout: 5s
      retries: 3

  # =============================================================================
  # EVENT STREAMING (KAFKA ECOSYSTEM)
  # =============================================================================
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper-enterprise
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SYNC_LIMIT: 2
      ZOOKEEPER_INIT_LIMIT: 5
      ZOOKEEPER_MAX_CLIENT_CNXNS: 60
      ZOOKEEPER_AUTOPURGE_SNAP_RETAIN_COUNT: 3
      ZOOKEEPER_AUTOPURGE_PURGE_INTERVAL: 24
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 3

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka-enterprise
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:29092,PLAINTEXT_HOST://0.0.0.0:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_NUM_NETWORK_THREADS: 8
      KAFKA_NUM_IO_THREADS: 8
      KAFKA_SOCKET_SEND_BUFFER_BYTES: 102400
      KAFKA_SOCKET_RECEIVE_BUFFER_BYTES: 102400
      KAFKA_SOCKET_REQUEST_MAX_BYTES: 104857600
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 1
      KAFKA_MIN_INSYNC_REPLICAS: 1
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
      KAFKA_LOG_CLEANUP_POLICY: delete
      KAFKA_COMPRESSION_TYPE: snappy
      KAFKA_BATCH_SIZE: 16384
      KAFKA_LINGER_MS: 5
      KAFKA_BUFFER_MEMORY: 33554432
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - enterprise-network
    ports:
      - "9092:9092"
      - "9101:9101"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  schema-registry:
    image: confluentinc/cp-schema-registry:7.4.0
    hostname: schema-registry
    container_name: schema-registry-enterprise
    depends_on:
      kafka:
        condition: service_healthy
    environment:
      SCHEMA_REGISTRY_HOST_NAME: schema-registry
      SCHEMA_REGISTRY_KAFKASTORE_BOOTSTRAP_SERVERS: kafka:29092
      SCHEMA_REGISTRY_LISTENERS: http://0.0.0.0:8081
      SCHEMA_REGISTRY_KAFKASTORE_TOPIC: _schemas
      SCHEMA_REGISTRY_KAFKASTORE_TOPIC_REPLICATION_FACTOR: 1
      SCHEMA_REGISTRY_SCHEMA_COMPATIBILITY_LEVEL: backward
    networks:
      - enterprise-network
    ports:
      - "8081:8081"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/subjects"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # DATABASE SERVICES
  # =============================================================================
  postgres:
    image: postgres:15-alpine
    hostname: postgres
    container_name: postgres-enterprise
    command: postgres -c shared_preload_libraries=pg_stat_statements -c pg_stat_statements.track=all -c max_connections=200 -c shared_buffers=256MB
    environment:
      POSTGRES_DB: x_marketing_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./logs/postgres:/var/log/postgresql
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d x_marketing_platform"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis:
    image: redis:7-alpine
    hostname: redis
    container_name: redis-enterprise
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru --save 900 1 --save 300 10 --save 60 10000 --maxclients 100 --timeout 300
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./logs/redis:/var/log/redis
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s

  # =============================================================================
  # API GATEWAY
  # =============================================================================
  kong:
    image: kong:latest
    hostname: kong
    container_name: kong-enterprise
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /kong/declarative/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: "0.0.0.0:8001"
      KONG_ADMIN_GUI_URL: "http://localhost:8002"
      KONG_PLUGINS: "bundled,cors,rate-limiting,response-transformer"
    volumes:
      - ./infrastructure/api-gateway/kong.yml:/kong/declarative/kong.yml:ro
    ports:
      - "8000:8000"
      - "8001:8001"
      - "8002:8002"
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # APPLICATION SERVICES
  # =============================================================================
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.enterprise
    hostname: backend
    container_name: backend-enterprise
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/x_marketing_platform
      - REDIS_URL=redis://redis:6379
      - KAFKA_BROKERS=kafka:29092
      - CONSUL_URL=http://consul:8500
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
      - LOG_LEVEL=info
      - METRICS_PORT=9092
      - USE_TESTCONTAINERS=false
      - DISABLE_PG_EXTENSIONS=true
      - KAFKAJS_NO_PARTITIONER_WARNING=1
      - DISABLE_KAFKA=true
      - DISABLE_REDIS=true
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      consul:
        condition: service_healthy
    volumes:
      - ./logs/backend:/app/logs
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  llm-service:
    build:
      context: ./llm-service
      dockerfile: Dockerfile.enterprise
    hostname: llm-service
    container_name: llm-service-enterprise
    ports:
      - "3003:3003"
    environment:
      - FLASK_ENV=production
      - PORT=3003
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - LOG_LEVEL=info
      - METRICS_PORT=9093
    volumes:
      - ./logs/llm-service:/app/logs
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  telegram-bot:
    build:
      context: ./telegram-bot
      dockerfile: Dockerfile.enterprise
    hostname: telegram-bot
    container_name: telegram-bot-enterprise
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - BACKEND_URL=http://backend:3001
      - LLM_SERVICE_URL=http://llm-service:3003
      - DATABASE_URL=********************************************/x_marketing_platform
      - REDIS_URL=redis://redis:6379
      - KAFKA_BROKERS=kafka:29092
      - CONSUL_URL=http://consul:8500
      - PORT=3002
      - LOG_LEVEL=info
      - ENABLE_POLLING=true
      - WEBHOOK_ENABLED=false
      - DISABLE_KAFKA=${DISABLE_KAFKA:-false}
      - METRICS_PORT=9091
      - USE_TESTCONTAINERS=false
      - KAFKAJS_NO_PARTITIONER_WARNING=1
      - DISABLE_REDIS=true
      - DOCKER_CONTAINER=true
      - SERVICE_TOKEN=${JWT_SECRET:-default-service-token}
      - BACKEND_SERVICE_TOKEN=${JWT_SECRET:-default-service-token}
    depends_on:
      postgres:
        condition: service_healthy
      consul:
        condition: service_healthy
    volumes:
      - ./logs/telegram-bot:/app/logs
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # OBSERVABILITY STACK
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    hostname: prometheus
    container_name: prometheus-enterprise
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/observability/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  grafana:
    image: grafana/grafana:latest
    hostname: grafana
    container_name: grafana-enterprise
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-enterprise_admin_2024}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./infrastructure/observability/grafana:/etc/grafana/provisioning
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  jaeger:
    image: jaegertracing/all-in-one:latest
    hostname: jaeger
    container_name: jaeger-enterprise
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
      - "9411:9411"
    networks:
      - enterprise-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:16686/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # EXPORTERS FOR METRICS
  # =============================================================================
  node-exporter:
    image: prom/node-exporter:latest
    hostname: node-exporter
    container_name: node-exporter-enterprise
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    networks:
      - enterprise-network
    restart: unless-stopped

  redis-exporter:
    image: oliver006/redis_exporter:latest
    hostname: redis-exporter
    container_name: redis-exporter-enterprise
    environment:
      - REDIS_ADDR=redis://redis:6379
    ports:
      - "9121:9121"
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - enterprise-network
    restart: unless-stopped

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    hostname: postgres-exporter
    container_name: postgres-exporter-enterprise
    environment:
      - DATA_SOURCE_NAME=********************************************/x_marketing_platform?sslmode=disable
    ports:
      - "9187:9187"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - enterprise-network
    restart: unless-stopped

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    hostname: cadvisor
    container_name: cadvisor-enterprise
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - enterprise-network
    restart: unless-stopped

  # =============================================================================
  # KAFKA MANAGEMENT UI
  # =============================================================================
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    hostname: kafka-ui
    container_name: kafka-ui-enterprise
    depends_on:
      kafka:
        condition: service_healthy
      schema-registry:
        condition: service_healthy
    environment:
      KAFKA_CLUSTERS_0_NAME: enterprise-cluster
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_SCHEMAREGISTRY: http://schema-registry:8081
      DYNAMIC_CONFIG_ENABLED: "true"
      AUTH_TYPE: "disabled"
    networks:
      - enterprise-network
    ports:
      - "8080:8080"
    restart: unless-stopped
    profiles:
      - management

  # =============================================================================
  # LOGGING STACK (OPTIONAL)
  # =============================================================================
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    hostname: elasticsearch
    container_name: elasticsearch-enterprise
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - enterprise-network
    restart: unless-stopped
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    hostname: kibana
    container_name: kibana-enterprise
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - enterprise-network
    restart: unless-stopped
    profiles:
      - logging

  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    hostname: fluentd
    container_name: fluentd-enterprise
    volumes:
      - ./infrastructure/fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
      - ./logs:/var/log/containers:ro
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    depends_on:
      - elasticsearch
    networks:
      - enterprise-network
    restart: unless-stopped
    profiles:
      - logging

  # =============================================================================
  # WEBHOOK MONITORING (OPTIONAL)
  # =============================================================================
  webhook-monitor:
    build:
      context: ./infrastructure/webhook-monitor
    hostname: webhook-monitor
    container_name: webhook-monitor-enterprise
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - WEBHOOK_URL=${WEBHOOK_DOMAIN}
    networks:
      - enterprise-network
    restart: unless-stopped
    profiles:
      - monitoring

  # =============================================================================
  # CLOUDFLARE TUNNEL (OPTIONAL)
  # =============================================================================
  cloudflared:
    image: cloudflare/cloudflared:latest
    hostname: cloudflared
    container_name: cloudflared-enterprise
    command: tunnel --no-autoupdate run --token ${CLOUDFLARE_TUNNEL_TOKEN}
    environment:
      - TUNNEL_TOKEN=${CLOUDFLARE_TUNNEL_TOKEN}
    networks:
      - enterprise-network
    restart: unless-stopped
    profiles:
      - tunnel

  # =============================================================================
  # NGINX REVERSE PROXY (OPTIONAL)
  # =============================================================================
  nginx:
    image: nginx:alpine
    hostname: nginx
    container_name: nginx-enterprise
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./infrastructure/nginx/ssl.conf:/etc/nginx/conf.d/ssl.conf:ro
      - ./data/certbot/conf:/etc/letsencrypt:ro
      - ./data/certbot/www:/var/www/certbot:ro
    depends_on:
      - backend
      - telegram-bot
    networks:
      - enterprise-network
    restart: unless-stopped
    profiles:
      - proxy

  # =============================================================================
  # SSL CERTIFICATE MANAGEMENT (OPTIONAL)
  # =============================================================================
  certbot:
    image: certbot/certbot
    hostname: certbot
    container_name: certbot-enterprise
    volumes:
      - ./data/certbot/conf:/etc/letsencrypt
      - ./data/certbot/www:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email admin@${WEBHOOK_DOMAIN} --agree-tos --no-eff-email -d ${WEBHOOK_DOMAIN}
    profiles:
      - ssl

volumes:
  consul-data:
    driver: local
  zookeeper-data:
    driver: local
  zookeeper-logs:
    driver: local
  kafka-data:
    driver: local
  postgres-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  elasticsearch-data:
    driver: local

networks:
  enterprise-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
