# Parallel Execution Strategy Configuration
# Phase 2: Performance & Caching Optimization

# Global parallel execution settings
global:
  max_parallel_jobs: 8
  fail_fast: false
  timeout_minutes: 30
  resource_optimization: true
  
# Matrix build configuration
matrix_strategy:
  # Dynamic matrix based on changes
  dynamic_matrix: true
  change_detection: true
  
  # Node.js version matrix
  nodejs:
    versions: ["18", "20"]
    default: "18"
    lts_only: false
    
  # Python version matrix  
  python:
    versions: ["3.9", "3.11"]
    default: "3.11"
    include_dev: false
    
  # Operating system matrix
  os:
    primary: "ubuntu-latest"
    additional: []  # Can add "windows-latest", "macos-latest" if needed
    
  # Service matrix
  services:
    - name: "backend"
      type: "nodejs"
      test_groups: ["unit", "integration", "e2e"]
      parallel_workers: 4
      
    - name: "frontend" 
      type: "nodejs"
      test_groups: ["unit", "integration", "e2e", "visual"]
      parallel_workers: 4
      
    - name: "telegram-bot"
      type: "nodejs"
      test_groups: ["unit", "integration"]
      parallel_workers: 2
      
    - name: "llm-service"
      type: "python"
      test_groups: ["unit", "integration", "model"]
      parallel_workers: 2

# Parallel testing configuration
parallel_testing:
  # Node.js testing
  nodejs:
    # Jest configuration
    jest:
      max_workers: 4
      run_in_band: false
      cache: true
      coverage: true
      parallel_coverage: true
      
    # Test splitting strategies
    test_splitting:
      strategy: "timing"  # or "count", "size"
      groups: 4
      balance_threshold: 0.8
      
    # E2E testing
    e2e:
      framework: "playwright"
      workers: 2
      retries: 2
      parallel_browsers: true
      
  # Python testing
  python:
    # Pytest configuration
    pytest:
      workers: 4  # pytest-xdist
      dist: "worksteal"
      cache: true
      coverage: true
      
    # Test splitting
    test_splitting:
      strategy: "duration"
      groups: 2
      balance_threshold: 0.7
      
    # Model testing
    model_testing:
      parallel_models: 2
      cache_models: true
      quantized_testing: true

# Service-specific parallel configurations
services:
  backend:
    # Build parallelization
    build:
      typescript_parallel: true
      prisma_parallel: true
      webpack_parallel: true
      
    # Testing parallelization
    testing:
      unit_tests:
        workers: 4
        timeout: "30s"
        coverage_threshold: 80
        
      integration_tests:
        workers: 2
        timeout: "60s"
        database_parallel: false  # Shared test DB
        
      e2e_tests:
        workers: 2
        timeout: "120s"
        browser_parallel: true
        
    # Linting and type checking
    quality:
      eslint_parallel: true
      typescript_check_parallel: true
      prettier_parallel: true
      
  frontend:
    # Build parallelization
    build:
      nextjs_parallel: true
      webpack_workers: 4
      typescript_parallel: true
      
    # Testing parallelization
    testing:
      unit_tests:
        workers: 4
        timeout: "30s"
        jsdom_parallel: true
        
      integration_tests:
        workers: 2
        timeout: "60s"
        api_mocking: true
        
      e2e_tests:
        workers: 2
        timeout: "180s"
        browsers: ["chromium", "firefox"]
        
      visual_tests:
        workers: 1  # Visual tests need consistency
        timeout: "90s"
        screenshot_parallel: false
        
    # Build optimization
    optimization:
      code_splitting: true
      tree_shaking: true
      parallel_minification: true
      
  telegram-bot:
    # Build parallelization
    build:
      typescript_parallel: true
      localization_parallel: true
      
    # Testing parallelization
    testing:
      unit_tests:
        workers: 2
        timeout: "30s"
        mock_telegram_api: true
        
      integration_tests:
        workers: 1  # Bot tests need sequential execution
        timeout: "90s"
        webhook_testing: true
        
    # Bot-specific optimizations
    bot_optimization:
      session_parallel: false  # Sessions must be sequential
      webhook_parallel: true
      command_parallel: true
      
  llm-service:
    # Build parallelization
    build:
      python_compile_parallel: true
      model_download_parallel: 2
      
    # Testing parallelization
    testing:
      unit_tests:
        workers: 4
        timeout: "45s"
        mock_models: true
        
      integration_tests:
        workers: 2
        timeout: "120s"
        real_models: false
        
      model_tests:
        workers: 1  # Model tests are resource intensive
        timeout: "300s"
        gpu_required: false
        
    # LLM-specific optimizations
    llm_optimization:
      model_loading_parallel: false  # Memory constraints
      inference_parallel: true
      embedding_parallel: 2
      
    # Twikit integration
    twikit_optimization:
      session_parallel: false  # X/Twitter sessions must be sequential
      proxy_rotation_parallel: true
      rate_limit_parallel: false

# Resource management
resource_management:
  # Memory allocation
  memory:
    nodejs_max_old_space: "4096"  # MB
    python_max_memory: "2048"     # MB
    docker_memory_limit: "6144"  # MB
    
  # CPU allocation
  cpu:
    nodejs_workers: "auto"  # Based on CPU cores
    python_workers: "auto"
    docker_cpu_limit: "4"
    
  # Disk I/O optimization
  disk:
    parallel_reads: true
    cache_optimization: true
    temp_dir_optimization: true

# Performance monitoring
performance_monitoring:
  # Execution time tracking
  timing:
    track_individual_tests: true
    track_build_stages: true
    track_parallel_efficiency: true
    
  # Resource usage tracking
  resources:
    memory_usage: true
    cpu_usage: true
    disk_io: true
    network_io: true
    
  # Parallel efficiency metrics
  efficiency:
    parallel_speedup: true
    load_balancing: true
    worker_utilization: true

# Optimization strategies
optimization_strategies:
  # Test optimization
  test_optimization:
    - name: "smart_test_selection"
      description: "Run only tests affected by changes"
      enabled: true
      
    - name: "test_result_caching"
      description: "Cache test results for unchanged code"
      enabled: true
      
    - name: "parallel_test_sharding"
      description: "Distribute tests across parallel workers"
      enabled: true
      
  # Build optimization
  build_optimization:
    - name: "incremental_builds"
      description: "Build only changed components"
      enabled: true
      
    - name: "parallel_compilation"
      description: "Compile multiple files simultaneously"
      enabled: true
      
    - name: "build_artifact_sharing"
      description: "Share build artifacts between jobs"
      enabled: true
      
  # Resource optimization
  resource_optimization:
    - name: "dynamic_worker_scaling"
      description: "Adjust workers based on available resources"
      enabled: true
      
    - name: "memory_pool_optimization"
      description: "Optimize memory allocation for parallel jobs"
      enabled: true
      
    - name: "io_parallelization"
      description: "Parallelize disk and network I/O operations"
      enabled: true

# Integration with caching
cache_integration:
  # Parallel cache operations
  parallel_cache:
    enabled: true
    max_concurrent_uploads: 4
    max_concurrent_downloads: 8
    
  # Cache warming
  cache_warming:
    parallel_warming: true
    warm_on_schedule: true
    warm_on_dependency_change: true
    
  # Cache analytics
  cache_analytics:
    parallel_analysis: true
    real_time_metrics: true

# Security integration
security_integration:
  # Maintain Phase 1 security
  preserve_security_scans: true
  parallel_security_checks: true
  
  # Parallel security scanning
  security_parallel:
    codeql_parallel: true
    trivy_parallel: true
    secret_scanning_parallel: true
    
# Twikit-specific parallel configuration
twikit_parallel:
  # Session management
  session_management:
    parallel_sessions: false  # Must be sequential for X/Twitter
    session_validation_parallel: true
    session_cleanup_parallel: true
    
  # Proxy management
  proxy_management:
    proxy_rotation_parallel: true
    proxy_health_check_parallel: 4
    proxy_performance_test_parallel: 2
    
  # Rate limiting
  rate_limiting:
    rate_limit_coordination: false  # Must be centralized
    rate_limit_monitoring_parallel: true
    
  # Anti-detection
  anti_detection:
    fingerprint_generation_parallel: true
    behavior_simulation_parallel: false  # Must be consistent
    
# Performance targets
performance_targets:
  parallel_efficiency: "70%"
  build_time_reduction: "50%"
  test_execution_speedup: "60%"
  resource_utilization: "80%"
  
# Monitoring and reporting
monitoring:
  enabled: true
  real_time_metrics: true
  
  metrics:
    - "parallel_execution_time"
    - "worker_utilization"
    - "load_balancing_efficiency"
    - "resource_contention"
    - "cache_hit_rate_parallel"
    
  reporting:
    format: "json"
    include_charts: true
    real_time_dashboard: true
