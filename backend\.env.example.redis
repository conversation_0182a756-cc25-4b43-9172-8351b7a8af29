# Upstash Redis Configuration
# Copy your connection details from Upstash Console

# Method 1: Full Redis URL (Recommended)
REDIS_URL=rediss://default:YOUR_PASSWORD@YOUR_HOST:YOUR_PORT

# Method 2: Individual components (Alternative)
REDIS_HOST=your-host.upstash.io
REDIS_PORT=6380
REDIS_PASSWORD=your-password
REDIS_USERNAME=default
REDIS_TLS=true

# Example from Upstash Console:
# REDIS_URL=rediss://default:<EMAIL>:6380

# Instructions:
# 1. Go to https://console.upstash.com/
# 2. Select your Redis database
# 3. Copy the "Redis Connect URL" or connection details
# 4. Replace the values above
# 5. Rename this file to .env.local or add to your existing .env file
