# Redis Configuration for Twikit Rate Limiting and Coordination
# Optimized for enterprise X/Twitter automation with distributed coordination

# Basic Configuration
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300
tcp-backlog 511

# Memory Management
maxmemory 1gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Persistence Configuration
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Append Only File (AOF) Configuration
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Logging
loglevel notice
logfile ""
syslog-enabled no

# Client Configuration
maxclients 10000

# Security
# requirepass your_redis_password_here
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command DEBUG ""

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency Monitoring
latency-monitor-threshold 100

# Advanced Configuration for Twikit
# Enable keyspace notifications for rate limiting coordination
notify-keyspace-events Ex

# Hash configuration for rate limiting data structures
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List configuration for queues
list-max-ziplist-size -2
list-compress-depth 0

# Set configuration for session tracking
set-max-intset-entries 512

# Sorted set configuration for time-based operations
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog configuration for analytics
hll-sparse-max-bytes 3000

# Stream configuration for event logging
stream-node-max-bytes 4096
stream-node-max-entries 100

# Lua scripting configuration
lua-time-limit 5000

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# TCP configuration
tcp-keepalive 300

# Threading (Redis 6.0+)
# io-threads 4
# io-threads-do-reads yes

# Memory usage optimization
activerehashing yes
client-query-buffer-limit 1gb
proto-max-bulk-len 512mb

# Replication (for future clustering)
# replica-serve-stale-data yes
# replica-read-only yes
# repl-diskless-sync no
# repl-diskless-sync-delay 5

# Modules (if needed)
# loadmodule /path/to/module.so

# Custom Lua scripts will be loaded by the application
# These are placeholders for the types of operations we'll perform

# Rate limiting script template (loaded by application):
# KEYS[1] = rate_limit_key
# ARGV[1] = limit
# ARGV[2] = window
# ARGV[3] = current_time

# Session coordination script template (loaded by application):
# KEYS[1] = session_key
# ARGV[1] = session_data
# ARGV[2] = ttl

# Proxy rotation script template (loaded by application):
# KEYS[1] = proxy_pool_key
# ARGV[1] = account_id
# ARGV[2] = rotation_strategy

# Analytics aggregation script template (loaded by application):
# KEYS[1] = analytics_key
# ARGV[1] = metric_data
# ARGV[2] = time_window

# Performance monitoring
info-refresh-interval 1

# Disable dangerous commands in production
# rename-command EVAL ""
# rename-command EVALSHA ""

# Enable protected mode for security
protected-mode no

# Disable RDB and AOF for testing (enable in production)
# save ""
# appendonly no

# Custom configuration for Twikit operations
# These settings are optimized for:
# 1. High-frequency rate limiting checks
# 2. Session state coordination
# 3. Real-time analytics
# 4. Proxy rotation coordination
# 5. Cross-instance synchronization

# Memory optimization for frequent small operations
hash-max-ziplist-entries 1024
list-max-ziplist-size -1
set-max-intset-entries 1024
zset-max-ziplist-entries 256

# Network optimization
tcp-backlog 2048
timeout 300

# Persistence tuned for rate limiting data
save 300 100
save 60 1000
save 10 10000

# AOF for durability of critical rate limiting state
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 50
auto-aof-rewrite-min-size 32mb

# Client connection limits
maxclients 20000

# Memory policy for rate limiting cache
maxmemory-policy volatile-ttl

# Keyspace notifications for coordination
notify-keyspace-events KEA

# Lua script cache
lua-time-limit 10000

# Slow log for debugging
slowlog-log-slower-than 5000
slowlog-max-len 256

# Latency tracking
latency-monitor-threshold 50

# Custom memory optimization
activerehashing yes
client-query-buffer-limit 2gb
proto-max-bulk-len 1gb

# Output buffer limits optimized for high throughput
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 512mb 128mb 60
client-output-buffer-limit pubsub 64mb 16mb 60

# Enable jemalloc stats
# jemalloc-bg-thread yes

# Disable swap usage
vm-enabled no

# Enable RDB-AOF hybrid persistence
aof-use-rdb-preamble yes

# Optimize for SSD storage
rdbcompression yes
rdbchecksum yes

# Network keepalive
tcp-keepalive 60

# Disable potentially dangerous commands
rename-command SHUTDOWN SHUTDOWN_TWIKIT_2024
rename-command CONFIG CONFIG_TWIKIT_2024

# Log level for debugging
loglevel notice

# Enable syslog for production monitoring
syslog-enabled no
syslog-ident redis-twikit

# Disable protected mode for Docker networking
protected-mode no

# Custom port (using default 6379)
port 6379

# Bind to all interfaces in Docker
bind 0.0.0.0

# Database count (using default 16)
databases 16

# Enable keyspace events for all key events and expired events
notify-keyspace-events Ex

# Optimize for memory usage patterns in rate limiting
maxmemory-samples 10

# Enable active memory defragmentation
# activedefrag yes
# active-defrag-ignore-bytes 100mb
# active-defrag-threshold-lower 10
# active-defrag-threshold-upper 100

# Optimize replica buffer
repl-backlog-size 64mb
repl-backlog-ttl 3600

# Client timeout
timeout 0

# Enable lazy freeing for better performance
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes

# Optimize hash table resize
activerehashing yes

# Set reasonable limits
client-query-buffer-limit 1gb
proto-max-bulk-len 512mb
