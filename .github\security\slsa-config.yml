# SLSA (Supply-chain Levels for Software Artifacts) Configuration
# For X/Twitter Automation Platform - Enterprise Security

# SLSA Level 3 Configuration
slsa:
  version: "1.0"
  level: 3
  
  # Build requirements for SLSA Level 3
  build:
    # Scripted build process
    scripted: true
    
    # Build service requirements
    service:
      # Use GitHub Actions as build service
      name: "GitHub Actions"
      version: "latest"
      
      # Build isolation requirements
      isolation:
        # Ephemeral environment
        ephemeral: true
        
        # Isolated from other builds
        isolated: true
        
        # Parameterless builds
        parameterless: false  # We use build parameters for multi-service
        
    # Build platform requirements
    platform:
      # Hosted build service
      hosted: true
      
      # Build service authentication
      authentication:
        # OIDC authentication required
        oidc: true
        
        # Multi-factor authentication
        mfa: true
        
      # Audit logging
      audit:
        enabled: true
        retention: "2 years"
        
  # Provenance requirements
  provenance:
    # Provenance generation
    generation:
      # Automatic generation
      automatic: true
      
      # Signed provenance
      signed: true
      
      # Provenance format
      format: "in-toto"
      
      # Provenance version
      version: "1.0"
      
    # Provenance content requirements
    content:
      # Build metadata
      build_metadata:
        # Build invocation ID
        invocation_id: true
        
        # Build start/end times
        timestamps: true
        
        # Build environment
        environment: true
        
        # Build parameters
        parameters: true
        
      # Source metadata
      source_metadata:
        # Source repository
        repository: true
        
        # Source commit
        commit: true
        
        # Source branch/tag
        ref: true
        
        # Source integrity
        integrity: true
        
      # Artifact metadata
      artifact_metadata:
        # Artifact hashes
        hashes: true
        
        # Artifact names
        names: true
        
        # Artifact sizes
        sizes: true
        
    # Provenance distribution
    distribution:
      # Public availability
      public: true
      
      # Provenance registry
      registry: "GitHub Releases"
      
      # Provenance verification
      verification:
        # Signature verification
        signature: true
        
        # Certificate verification
        certificate: true
        
        # Policy verification
        policy: true

# Service-specific SLSA configuration
services:
  # Backend service
  backend:
    artifacts:
      - name: "backend-build.tar.gz"
        type: "application/gzip"
        description: "Backend Node.js application build"
        
    dependencies:
      # Node.js dependencies
      nodejs:
        package_manager: "npm"
        lockfile: "package-lock.json"
        audit: true
        
      # System dependencies
      system:
        base_image: "node:18-alpine"
        packages: []
        
    build_steps:
      - "npm ci --only=production"
      - "npm run build"
      - "tar creation with reproducible flags"
      
  # Frontend service
  frontend:
    artifacts:
      - name: "frontend-build.tar.gz"
        type: "application/gzip"
        description: "Frontend Next.js application build"
        
    dependencies:
      # Node.js dependencies
      nodejs:
        package_manager: "npm"
        lockfile: "package-lock.json"
        audit: true
        
      # System dependencies
      system:
        base_image: "node:18-alpine"
        packages: []
        
    build_steps:
      - "npm ci --only=production"
      - "npm run build"
      - "tar creation with reproducible flags"
      
  # Telegram Bot service
  telegram-bot:
    artifacts:
      - name: "telegram-bot-build.tar.gz"
        type: "application/gzip"
        description: "Telegram Bot Node.js application build"
        
    dependencies:
      # Node.js dependencies
      nodejs:
        package_manager: "npm"
        lockfile: "package-lock.json"
        audit: true
        
      # System dependencies
      system:
        base_image: "node:18-alpine"
        packages: []
        
    build_steps:
      - "npm ci --only=production"
      - "npm run build"
      - "tar creation with reproducible flags"
      
  # LLM Service
  llm-service:
    artifacts:
      - name: "llm-service-build.tar.gz"
        type: "application/gzip"
        description: "LLM Python service build"
        
    dependencies:
      # Python dependencies
      python:
        package_manager: "pip"
        requirements: "requirements.txt"
        audit: true
        
      # System dependencies
      system:
        base_image: "python:3.11-slim"
        packages: ["build-essential", "curl"]
        
    build_steps:
      - "pip install -r requirements.txt"
      - "python -m py_compile *.py"
      - "tar creation with reproducible flags"

# Container image SLSA configuration
containers:
  # Multi-platform builds
  platforms:
    - "linux/amd64"
    - "linux/arm64"
    
  # Base image requirements
  base_images:
    allowed:
      - "node:18-alpine"
      - "python:3.11-slim"
      - "postgres:15-alpine"
      - "redis:7-alpine"
      
    verification:
      # Verify base image signatures
      signatures: true
      
      # Verify base image provenance
      provenance: true
      
      # Scan base images for vulnerabilities
      vulnerability_scan: true
      
  # Build requirements
  build:
    # Reproducible builds
    reproducible: true
    
    # Build attestations
    attestations:
      # SBOM generation
      sbom: true
      
      # Provenance generation
      provenance: true
      
      # Vulnerability scan results
      vulnerability_scan: true
      
    # Build security
    security:
      # Run as non-root
      non_root: true
      
      # Read-only root filesystem
      read_only_root: false  # Some services need write access
      
      # No privileged containers
      no_privileged: true

# Verification policies
verification:
  # Required verifications before deployment
  required:
    # Provenance verification
    provenance: true
    
    # Signature verification
    signature: true
    
    # Policy compliance
    policy: true
    
    # Vulnerability scan
    vulnerability_scan: true
    
  # Verification tools
  tools:
    # SLSA verifier
    slsa_verifier:
      version: "latest"
      source: "github.com/slsa-framework/slsa-verifier"
      
    # Cosign for signature verification
    cosign:
      version: "latest"
      source: "github.com/sigstore/cosign"
      
    # Syft for SBOM verification
    syft:
      version: "latest"
      source: "github.com/anchore/syft"

# Compliance and reporting
compliance:
  # Compliance frameworks
  frameworks:
    - "SLSA Level 3"
    - "NIST SSDF"
    - "CISA Secure Software Development"
    
  # Reporting requirements
  reporting:
    # Generate compliance reports
    generate: true
    
    # Report format
    format: "json"
    
    # Report retention
    retention: "2 years"
    
    # Report distribution
    distribution:
      - "security team"
      - "compliance team"
      - "development team"

# Integration with X/Twitter automation security
x_automation:
  # Twikit integration verification
  twikit:
    # Verify Twikit library provenance
    verify_dependencies: true
    
    # Check for secure session handling
    session_security: true
    
    # Verify anti-detection measures
    anti_detection: true
    
  # API security verification
  api_security:
    # Verify API key handling
    credential_security: true
    
    # Verify rate limiting implementation
    rate_limiting: true
    
    # Verify encryption implementation
    encryption: true
    
  # Multi-service communication
  inter_service:
    # Verify service-to-service authentication
    authentication: true
    
    # Verify encrypted communication
    encryption: true
    
    # Verify service mesh security
    service_mesh: false  # Not using service mesh currently
