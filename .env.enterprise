# Enterprise Environment Configuration
# This file contains all environment variables for the enterprise-grade system

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=3002
SERVICE_HOST=telegram-bot
LOG_LEVEL=info

# =============================================================================
# TELEGRAM BOT CONFIGURATION
# =============================================================================
TELEGRAM_BOT_TOKEN=**********************************************
# WEBHOOK_URL=https://enterprise-bot.ngrok-free.app/webhook/telegram
ENABLE_POLLING=true
DISABLE_KAFKA=true

# Bot Configuration
BOT_USERNAME=enterprise_content_bot
BOT_NAME="Enterprise Content Bot"
BOT_DESCRIPTION="Advanced AI-powered content generation and marketing automation platform"
BOT_ABOUT="🤖 Enterprise-grade AI assistant for content creation, marketing campaigns, and business automation. Powered by Gemini AI with advanced analytics and multi-platform support."
BOT_SHORT_DESCRIPTION="AI-powered enterprise content & marketing automation"

# Webhook Configuration
# TELEGRAM_WEBHOOK_URL=https://your-domain.com/webhook/telegram
CLOUDFLARE_WEBHOOK_URL=https://your-tunnel.trycloudflare.com/webhook/telegram
BACKUP_WEBHOOK_URL=https://backup-domain.com/webhook/telegram
WEBHOOK_SECRET_TOKEN=auto_generated
WEBHOOK_MAX_CONNECTIONS=100
WEBHOOK_ALLOWED_UPDATES=message,callback_query,inline_query,chosen_inline_result
WEBHOOK_DROP_PENDING_UPDATES=true

# Domain and SSL Configuration
WEBHOOK_DOMAIN=your-domain.com
SSL_CERTIFICATE_PATH=/etc/ssl/certs/telegram-bot.crt
SSL_PRIVATE_KEY_PATH=/etc/ssl/private/telegram-bot.key
SSL_CHECK_INTERVAL=86400000

# Cloudflare Configuration
CLOUDFLARE_TUNNEL_TOKEN=your_cloudflare_tunnel_token_here
CLOUDFLARE_ZONE_ID=your_cloudflare_zone_id_here
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token_here

# =============================================================================
# SERVICE DISCOVERY (CONSUL)
# =============================================================================
CONSUL_HOST=consul
CONSUL_PORT=8500
CONSUL_SECURE=false
CONSUL_DATACENTER=dc1
CONSUL_TOKEN=enterprise_consul_token_2024
CONSUL_URL=http://consul:8500
CONSUL_HEALTH_CHECK_INTERVAL=10s
CONSUL_HEALTH_CHECK_TIMEOUT=5s
CONSUL_SERVICE_TTL=30s
CONSUL_UI_ENABLED=true
CONSUL_CONNECT_ENABLED=false

# =============================================================================
# SERVICE URLS & ENDPOINTS
# =============================================================================
# Core Services
TELEGRAM_BOT_URL=http://telegram-bot:3002
BACKEND_API_URL=http://backend:3001
LLM_SERVICE_URL=http://llm-service:3003
NATURAL_LANGUAGE_SERVICE_URL=http://natural-language-service:3004
CAMPAIGN_ORCHESTRATOR_URL=http://campaign-orchestrator:3005
CONTENT_OPTIMIZER_URL=http://content-optimizer:3006

# Infrastructure Services
KONG_ADMIN_URL=http://kong:8001
KONG_PROXY_URL=http://kong:8000
REDIS_URL=redis://redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
POSTGRES_URL=********************************************/x_marketing_platform
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=x_marketing_platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_SSL=false
POSTGRES_MAX_CONNECTIONS=100
POSTGRES_CONNECTION_TIMEOUT=30000

# External Services
WEBHOOK_HEALTH_CHECK_URL=https://hc-ping.com/a1b2c3d4-e5f6-7890-abcd-ef1234567890
UPTIME_ROBOT_API_URL=https://api.uptimerobot.com/v2/
UPTIME_ROBOT_API_KEY=ur123456-abcdef1234567890abcdef1234567890abcdef12
STATUS_PAGE_URL=https://status.enterprise-ai-bot.com
CLOUDFLARE_API_URL=https://api.cloudflare.com/client/v4
CLOUDFLARE_API_TOKEN=ABC123456789DEFGHIJKLMNOPQRSTUVWXYZabcdefghijk
CLOUDFLARE_ZONE_ID=****************************************

# =============================================================================
# EVENT STREAMING (KAFKA)
# =============================================================================
KAFKA_BROKERS=kafka:29092
KAFKA_CLIENT_ID=enterprise-telegram-bot-service
KAFKA_GROUP_ID=enterprise-telegram-bot-consumer-group
KAFKA_USERNAME=
KAFKA_PASSWORD=
KAFKA_SASL_MECHANISM=
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_SSL_ENABLED=false
KAFKA_AUTO_OFFSET_RESET=earliest
KAFKA_ENABLE_AUTO_COMMIT=true
KAFKA_SESSION_TIMEOUT=30000
KAFKA_HEARTBEAT_INTERVAL=3000
KAFKA_MAX_POLL_RECORDS=500
KAFKA_FETCH_MAX_WAIT=500
KAFKA_RETRY_ATTEMPTS=5
KAFKA_RETRY_DELAY=1000
KAFKA_TOPICS_PREFIX=enterprise_bot_
KAFKA_REPLICATION_FACTOR=3
KAFKA_PARTITIONS=12

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=********************************************/x_marketing_platform
DATABASE_POOL_SIZE=20
DATABASE_CONNECTION_TIMEOUT=30000

# =============================================================================
# CACHE CONFIGURATION (REDIS)
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=telegram_bot:

# =============================================================================
# SERVICE URLS
# =============================================================================
BACKEND_URL=http://kong:8000/api
LLM_SERVICE_URL=http://kong:8000/llm

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
JWT_SECRET=Ent3rpr1s3_JWT_S3cr3t_K3y_2024_ABC123456789DEFGHIJKLMNOPQRSTUVWXYZabcdefghijk
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=Ent3rpr1s3_JWT_R3fr3sh_S3cr3t_K3y_2024_XYZ987654321FEDCBAzyxwvutsrqponmlkjih
JWT_REFRESH_EXPIRES_IN=7d
TELEGRAM_BOT_JWT_SECRET=T3l3gr@m_B0t_JWT_S3cr3t_2024_ABC123456789DEFGHIJKLMNOPQRSTUVWXYZabcdefghijk
BACKEND_SERVICE_JWT_SECRET=B@ck3nd_S3rv1c3_JWT_S3cr3t_2024_XYZ987654321FEDCBAzyxwvutsrqponmlkjih
LLM_SERVICE_JWT_SECRET=LLM_S3rv1c3_JWT_S3cr3t_2024_ABC123456789DEFGHIJKLMNOPQRSTUVWXYZabcdefghijk
API_KEY=Ent3rpr1s3_API_K3y_2024_ABC123456789DEFGHIJKLMNOPQRSTUVWXYZabcdefghijk
API_SECRET=Ent3rpr1s3_API_S3cr3t_2024_XYZ987654321FEDCBAzyxwvutsrqponmlkjih
ENCRYPTION_KEY=Ent3rpr1s3_Encr_K3y_2024_ABC123456789DEFGHIJKLMNOPQRSTUVWXYZabcdefghijk
ENCRYPTION_IV=Ent3rpr1s3_IV_2024_XYZ987654321FEDC
WEBHOOK_SECRET=Ent3rpr1s3_W3bh00k_S3cr3t_2024_ABC123456789DEFGHIJKLMNOPQRSTUVWXYZabcdefghijk
SESSION_SECRET=Ent3rpr1s3_S3ss10n_S3cr3t_2024_XYZ987654321FEDCBAzyxwvutsrqponmlkjih

# =============================================================================
# OBSERVABILITY CONFIGURATION
# =============================================================================

# Metrics
METRICS_PORT=9091
ENABLE_METRICS=true

# Distributed Tracing (Jaeger)
JAEGER_ENDPOINT=http://jaeger:14268/api/traces
ENABLE_TRACING=true

# Health Checks
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# CIRCUIT BREAKER CONFIGURATION
# =============================================================================
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RESET_TIMEOUT=60000
CIRCUIT_BREAKER_TIMEOUT=30000

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_SKIP_FAILED_REQUESTS=true

# =============================================================================
# CACHING CONFIGURATION
# =============================================================================
CACHE_ENABLED=true
CACHE_TTL=300000
CACHE_MAX_SIZE=1000

# =============================================================================
# RETRY CONFIGURATION
# =============================================================================
RETRY_ATTEMPTS=3
RETRY_DELAY=1000
RETRY_MAX_DELAY=30000

# =============================================================================
# TIMEOUT CONFIGURATION
# =============================================================================
HTTP_TIMEOUT=30000
LLM_TIMEOUT=120000
DATABASE_TIMEOUT=10000

# =============================================================================
# CLOUDFLARE TUNNEL (WEBHOOK)
# =============================================================================
CLOUDFLARE_TUNNEL_TOKEN=your_cloudflare_tunnel_token_here

# =============================================================================
# LLM SERVICE CONFIGURATION
# =============================================================================
GEMINI_API_KEY=AIzaSyC8XYZ123456789abcdefghijklmnopqrstuvwxyz_ENTERPRISE
GEMINI_MODEL=gemini-2.5-flash
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=8192
LLM_SERVICE_URL=http://llm-service:3003
LLM_SERVICE_TIMEOUT=120000
LLM_SERVICE_RETRIES=3

# Advanced Gemini Configuration
GEMINI_ENTERPRISE_MODEL=gemini-2.5-flash
GEMINI_VISION_MODEL=gemini-2.5-flash
GEMINI_REASONING_MODEL=gemini-2.5-flash
GEMINI_MULTIMODAL_ENABLED=true
GEMINI_DEEP_THINK_ENABLED=true
GEMINI_FUNCTION_CALLING_ENABLED=true
GEMINI_SAFETY_SETTINGS=BLOCK_MEDIUM_AND_ABOVE
GEMINI_HARM_CATEGORY_HARASSMENT=BLOCK_MEDIUM_AND_ABOVE
GEMINI_HARM_CATEGORY_HATE_SPEECH=BLOCK_MEDIUM_AND_ABOVE
GEMINI_HARM_CATEGORY_SEXUALLY_EXPLICIT=BLOCK_MEDIUM_AND_ABOVE
GEMINI_HARM_CATEGORY_DANGEROUS_CONTENT=BLOCK_MEDIUM_AND_ABOVE

# =============================================================================
# EXTERNAL API KEYS
# =============================================================================
OPENAI_API_KEY=sk-proj-ABC123456789DEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz
ANTHROPIC_API_KEY=sk-ant-api03-ABC123456789DEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz
COHERE_API_KEY=ABC123456789DEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
PROMETHEUS_URL=http://prometheus:9090
GRAFANA_URL=http://grafana:3000
GRAFANA_ADMIN_USER=admin
GRAFANA_PASSWORD=enterprise_admin_2024
PROMETHEUS_RETENTION_TIME=30d
JAEGER_ENDPOINT=http://jaeger:14268/api/traces
JAEGER_AGENT_HOST=jaeger
JAEGER_AGENT_PORT=6832
JAEGER_COLLECTOR_ENDPOINT=http://jaeger:14268/api/traces
JAEGER_QUERY_ENDPOINT=http://jaeger:16686

# Metrics Configuration
METRICS_PORT=9091
METRICS_PATH=/metrics
METRICS_ENABLED=true
METRICS_COLLECTION_INTERVAL=15000
METRICS_RETENTION_DAYS=30

# Tracing Configuration
TRACING_ENABLED=true
TRACING_SAMPLE_RATE=1.0
TRACING_SERVICE_NAME=enterprise-telegram-bot
TRACING_SERVICE_VERSION=1.0.0
TRACING_ENVIRONMENT=production

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_TIMESTAMP=true
LOG_CORRELATION_ID=true
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/var/log/telegram-bot/app.log
LOG_FILE_MAX_SIZE=100MB
LOG_FILE_MAX_FILES=10
LOG_CONSOLE_ENABLED=true
LOG_STRUCTURED=true

# Alert Configuration
ALERT_WEBHOOK_URL=https://hooks.slack.com/services/YOUR_WORKSPACE/YOUR_CHANNEL/YOUR_TOKEN
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token-here
SLACK_SIGNING_SECRET=your-slack-signing-secret-here
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR_WEBHOOK_ID/YOUR_WEBHOOK_TOKEN
TEAMS_WEBHOOK_URL=https://outlook.office.com/webhook/YOUR_TEAMS_WEBHOOK_URL
ALERT_EMAIL_ENABLED=true
ALERT_SLACK_ENABLED=true
ALERT_DISCORD_ENABLED=false
ALERT_TEAMS_ENABLED=false

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
NODE_OPTIONS=--max-old-space-size=512
UV_THREADPOOL_SIZE=16
WORKERS=4

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_ANALYTICS=true
ENABLE_CONTENT_GENERATION=true
ENABLE_TEMPLATES=true
ENABLE_USER_SYNC=true
ENABLE_NOTIFICATIONS=true
ENABLE_AUTOMATION=true
ENABLE_COMPLIANCE=true
ENABLE_QUALITY_CONTROL=true

# =============================================================================
# DEVELOPMENT & DEBUGGING
# =============================================================================
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=30

# =============================================================================
# COMPLIANCE & SECURITY
# =============================================================================
ENABLE_AUDIT_LOGGING=true
ENABLE_DATA_ENCRYPTION=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true
ENABLE_HELMET=true

# =============================================================================
# SCALING CONFIGURATION
# =============================================================================
AUTO_SCALING_ENABLED=true
MIN_REPLICAS=2
MAX_REPLICAS=10
CPU_THRESHOLD=70
MEMORY_THRESHOLD=80

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
NOTIFICATION_CHANNELS=telegram,email,slack
SLACK_WEBHOOK_URL=https://hooks.slack.com/your-webhook-url
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-email-password

# =============================================================================
# CONTENT MODERATION
# =============================================================================
ENABLE_CONTENT_MODERATION=true
MODERATION_STRICT_MODE=false
BLOCKED_WORDS_LIST=spam,scam,fraud

# =============================================================================
# ANALYTICS & REPORTING
# =============================================================================
ANALYTICS_RETENTION_DAYS=90
ENABLE_REAL_TIME_ANALYTICS=true
ANALYTICS_BATCH_SIZE=1000

# =============================================================================
# INTEGRATION SETTINGS
# =============================================================================
SYNC_INTERVAL=300000
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_TIMEOUT=10000

# =============================================================================
# ENTERPRISE FEATURES
# =============================================================================
ENTERPRISE_MODE=true
MULTI_TENANT=false
CUSTOM_BRANDING=false
ADVANCED_ANALYTICS=true
PRIORITY_SUPPORT=true

# =============================================================================
# DISASTER RECOVERY
# =============================================================================
DR_ENABLED=true
DR_BACKUP_LOCATION=s3://your-backup-bucket
DR_RTO=4h
DR_RPO=1h

# =============================================================================
# COMPLIANCE FRAMEWORKS
# =============================================================================
GDPR_COMPLIANCE=true
SOC2_COMPLIANCE=true
HIPAA_COMPLIANCE=false
PCI_COMPLIANCE=false

# =============================================================================
# PRODUCTION DEPLOYMENT SETTINGS
# =============================================================================
NODE_ENV=production
LOG_LEVEL=info
ENABLE_CLUSTERING=true
CLUSTER_WORKERS=auto

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=enterprise-backups
BACKUP_S3_ACCESS_KEY=your_s3_access_key_here
BACKUP_S3_SECRET_KEY=your_s3_secret_key_here
BACKUP_S3_REGION=us-east-1

# Alert Configuration
ALERT_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
ALERT_EMAIL_SMTP_HOST=smtp.gmail.com
ALERT_EMAIL_SMTP_PORT=587
ALERT_EMAIL_USER=<EMAIL>
ALERT_EMAIL_PASS=your_email_password_here
ALERT_EMAIL_FROM="Enterprise Platform <<EMAIL>>"
ALERT_EMAIL_TO=<EMAIL>,<EMAIL>

# Security Configuration
SECURITY_SCAN_ENABLED=true
SECURITY_SCAN_SCHEDULE="0 3 * * *"
VULNERABILITY_SCAN_ENABLED=true
COMPLIANCE_MONITORING_ENABLED=true

# Performance Monitoring
PERFORMANCE_MONITORING_ENABLED=true
APM_SERVICE_NAME=enterprise-platform
APM_ENVIRONMENT=production
APM_SERVER_URL=http://apm-server:8200

# Auto Scaling
AUTO_SCALING_ENABLED=true
AUTO_SCALING_MIN_INSTANCES=2
AUTO_SCALING_MAX_INSTANCES=10
AUTO_SCALING_TARGET_CPU=70
AUTO_SCALING_TARGET_MEMORY=80

# Enterprise Integrations
LDAP_ENABLED=false
LDAP_URL=ldap://ldap.enterprise.com:389
LDAP_BIND_DN=cn=admin,dc=enterprise,dc=com
LDAP_BIND_PASSWORD=ldap_password_here
LDAP_SEARCH_BASE=ou=users,dc=enterprise,dc=com

SAML_ENABLED=false
SAML_ENTRY_POINT=https://sso.enterprise.com/saml/login
SAML_ISSUER=enterprise-platform
SAML_CERT_PATH=/etc/ssl/certs/saml.crt

OAUTH_ENABLED=true
OAUTH_GOOGLE_CLIENT_ID=123456789012-abc123456789def123456789abc123456789.apps.googleusercontent.com
OAUTH_GOOGLE_CLIENT_SECRET=GOCSPX-ABC123456789DEFGHIJKLMNOPqr
OAUTH_GITHUB_CLIENT_ID=Iv1.abc123456789def12
OAUTH_GITHUB_CLIENT_SECRET=abc123456789def123456789abc123456789def1234
OAUTH_MICROSOFT_CLIENT_ID=abc12345-6789-def1-2345-6789abcdef123456
OAUTH_MICROSOFT_CLIENT_SECRET=ABC~123456789DEFGHIJKLMNOPQRSTUVWXYZabc
OAUTH_LINKEDIN_CLIENT_ID=123456789abc
OAUTH_LINKEDIN_CLIENT_SECRET=ABC123456789DEFGHIJKLMNOPqr

# =============================================================================
# ENTERPRISE FEATURE CONFIGURATION
# =============================================================================
# Advanced AI Features
ENABLE_MULTIMODAL=true
ENABLE_DEEP_THINK=true
ENABLE_FUNCTION_CALLING=true
ENABLE_CODE_GENERATION=true
ENABLE_IMAGE_ANALYSIS=true
ENABLE_VIDEO_PROCESSING=true
ENABLE_AUDIO_TRANSCRIPTION=true
ENABLE_DOCUMENT_PARSING=true

# Campaign & Content Features
ENABLE_CAMPAIGN_ORCHESTRATION=true
ENABLE_CONTENT_OPTIMIZATION=true
ENABLE_A_B_TESTING=true
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_COMPETITOR_ANALYSIS=true
ENABLE_TREND_ANALYSIS=true
ENABLE_BRAND_MONITORING=true
ENABLE_INFLUENCER_DISCOVERY=true

# Automation Features
ENABLE_AUTOMATION=true
ENABLE_SCHEDULED_CAMPAIGNS=true
ENABLE_TRIGGER_BASED_AUTOMATION=true
ENABLE_WORKFLOW_AUTOMATION=true
ENABLE_BULK_OPERATIONS=true
ENABLE_BATCH_PROCESSING=true

# Analytics & Reporting
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_REAL_TIME_ANALYTICS=true
ENABLE_CUSTOM_DASHBOARDS=true
ENABLE_EXPORT_REPORTS=true
ENABLE_DATA_VISUALIZATION=true
ENABLE_PREDICTIVE_ANALYTICS=true

# Collaboration Features
ENABLE_REAL_TIME_COLLABORATION=true
ENABLE_TEAM_WORKSPACES=true
ENABLE_ROLE_BASED_ACCESS=true
ENABLE_APPROVAL_WORKFLOWS=true
ENABLE_COMMENT_SYSTEM=true
ENABLE_VERSION_CONTROL=true

# Enterprise Security
ENABLE_ENTERPRISE_SECURITY=true
ENABLE_SSO_INTEGRATION=true
ENABLE_TWO_FACTOR_AUTH=true
ENABLE_IP_WHITELISTING=true
ENABLE_API_KEY_ROTATION=true
ENABLE_ENCRYPTION_AT_REST=true
ENABLE_ENCRYPTION_IN_TRANSIT=true

# Compliance & Governance
ENABLE_COMPLIANCE_MONITORING=true
ENABLE_AUDIT_LOGGING=true
ENABLE_DATA_RETENTION_POLICIES=true
ENABLE_GDPR_COMPLIANCE=true
ENABLE_CCPA_COMPLIANCE=true
ENABLE_SOC2_COMPLIANCE=true
ENABLE_HIPAA_COMPLIANCE=false

# White Label & Customization
ENABLE_WHITE_LABEL=true
ENABLE_CUSTOM_BRANDING=true
ENABLE_CUSTOM_DOMAINS=true
ENABLE_CUSTOM_THEMES=true
ENABLE_CUSTOM_INTEGRATIONS=true

# =============================================================================
# ENTERPRISE LIMITS & QUOTAS
# =============================================================================
# User & Organization Limits
MAX_USERS_PER_ORGANIZATION=10000
MAX_ORGANIZATIONS=1000
MAX_TEAMS_PER_ORGANIZATION=100
MAX_PROJECTS_PER_TEAM=500

# Content & Campaign Limits
MAX_CAMPAIGNS_PER_USER=1000
MAX_CONTENT_GENERATIONS_PER_DAY=10000
MAX_CONTENT_LENGTH=50000
MAX_CAMPAIGN_DURATION_DAYS=365
MAX_SCHEDULED_CAMPAIGNS=1000

# API & Request Limits
MAX_API_REQUESTS_PER_MINUTE=1000
MAX_API_REQUESTS_PER_HOUR=50000
MAX_API_REQUESTS_PER_DAY=1000000
MAX_WEBHOOK_ENDPOINTS=100
MAX_CONCURRENT_REQUESTS=100

# Storage & Media Limits
MAX_STORAGE_PER_USER_GB=100
MAX_TOTAL_STORAGE_TB=10
MAX_IMAGE_SIZE_MB=50
MAX_VIDEO_SIZE_MB=500
MAX_AUDIO_SIZE_MB=100
MAX_DOCUMENT_SIZE_MB=100

# Processing Limits
MAX_PROCESSING_TIME_SECONDS=300
MAX_QUEUE_SIZE=10000
MAX_RETRY_ATTEMPTS=5
MAX_BATCH_SIZE=1000
CIRCUIT_BREAKER_THRESHOLD=10
CIRCUIT_BREAKER_TIMEOUT=60000

# Retention & Backup
STORAGE_RETENTION_DAYS=365
BACKUP_RETENTION_DAYS=90
LOG_RETENTION_DAYS=30
ANALYTICS_RETENTION_DAYS=730

# =============================================================================
# ENTERPRISE CONTACT & SUPPORT
# =============================================================================
COMPANY_NAME="Enterprise AI Solutions"
COMPANY_WEBSITE=https://enterprise-ai.com
SUPPORT_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>
TECHNICAL_CONTACT=<EMAIL>
SALES_CONTACT=<EMAIL>
BILLING_CONTACT=<EMAIL>
LEGAL_CONTACT=<EMAIL>

# Support Configuration
ENABLE_ENTERPRISE_SUPPORT=true
SUPPORT_TICKET_SYSTEM_URL=https://support.enterprise-ai.com
SUPPORT_CHAT_ENABLED=true
SUPPORT_PHONE_ENABLED=true
SUPPORT_SLA_RESPONSE_TIME_HOURS=4
SUPPORT_SLA_RESOLUTION_TIME_HOURS=24

# Documentation & Resources
DOCUMENTATION_URL=https://docs.enterprise-ai.com
API_DOCUMENTATION_URL=https://api-docs.enterprise-ai.com
DEVELOPER_PORTAL_URL=https://developers.enterprise-ai.com
STATUS_PAGE_URL=https://status.enterprise-ai.com
COMMUNITY_FORUM_URL=https://community.enterprise-ai.com
