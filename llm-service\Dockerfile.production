# Production-Grade Dockerfile for LLM Service
# Multi-stage build with security hardening and optimization

# Build stage
FROM python:3.11-slim as builder

# Set build arguments
ARG NODE_ENV=production
ARG BUILD_DATE
ARG VERSION
ARG VCS_REF

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r llmservice && useradd -r -g llmservice llmservice

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt requirements-prod.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements-prod.txt

# Production stage
FROM python:3.11-slim as production

# Set labels for metadata
LABEL maintainer="X Marketing Platform Team" \
      version="${VERSION}" \
      description="Production LLM Service with Natural Language Orchestrator" \
      build-date="${BUILD_DATE}" \
      vcs-ref="${VCS_REF}"

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    NODE_ENV=production \
    FLASK_ENV=production \
    PYTHONPATH=/app

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r llmservice && useradd -r -g llmservice llmservice

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/tmp && \
    chown -R llmservice:llmservice /app

# Set working directory
WORKDIR /app

# Copy Python packages from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy application code
COPY --chown=llmservice:llmservice . .

# Create production configuration
COPY --chown=llmservice:llmservice config/production.py config/production.py

# Set proper permissions
RUN chmod +x scripts/entrypoint.sh && \
    chmod -R 755 /app && \
    chmod -R 644 /app/config && \
    chmod -R 755 /app/scripts

# Switch to non-root user
USER llmservice

# Expose ports
EXPOSE 3005 9090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3005/health || exit 1

# Set entrypoint
ENTRYPOINT ["./scripts/entrypoint.sh"]

# Default command
CMD ["python", "fastapi_app.py"]
