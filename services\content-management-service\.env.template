# Content Management Service Environment Template
# Copy this file to .env.local and fill in the actual values

# Application Configuration
NODE_ENV=development
PORT=3014
HOST=localhost

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/x_marketing_platform
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_KEY_PREFIX=content-mgmt:

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=content-management-service
KAFKA_GROUP_ID=content-management-group
DISABLE_KAFKA=false

# Consul Configuration
CONSUL_HOST=localhost
CONSUL_PORT=8500
CONSUL_SERVICE_NAME=content-management-service
DISABLE_CONSUL=false

# LLM Service Configuration
LLM_SERVICE_URL=http://localhost:3003
LLM_API_KEY=your-llm-api-key
LLM_MODEL=gpt-3.5-turbo

# JWT Configuration (REQUIRED - Generate secure keys)
JWT_SECRET=your-super-secret-jwt-key-that-should-be-at-least-32-characters-long
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key-that-should-be-at-least-32-characters-long

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CONTENT_RATE_LIMIT_MAX=30

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9094
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Service Discovery
SERVICE_VERSION=1.0.0
SERVICE_TAGS=content-management,media-processing,ai-generation,microservice

# Health Checks
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Enterprise Features
ENTERPRISE_MODE=true
ENABLE_AUDIT_LOGGING=true
ENABLE_SECURITY_MONITORING=true

# Content Management Specific
MAX_CONTENT_PER_USER=1000
MAX_MEDIA_SIZE=********
MAX_MEDIA_PER_CONTENT=10
CONTENT_PROCESSING_TIMEOUT=60000
AI_GENERATION_TIMEOUT=30000

# Storage Configuration
STORAGE_PROVIDER=local
STORAGE_BUCKET=x-marketing-content
STORAGE_REGION=us-east-1
STORAGE_ACCESS_KEY=
STORAGE_SECRET_KEY=
STORAGE_ENDPOINT=
STORAGE_LOCAL_PATH=./uploads

# External Services
BACKEND_SERVICE_URL=http://localhost:3001
USER_MANAGEMENT_SERVICE_URL=http://localhost:3011
ACCOUNT_MANAGEMENT_SERVICE_URL=http://localhost:3012
CAMPAIGN_MANAGEMENT_SERVICE_URL=http://localhost:3013
TELEGRAM_BOT_URL=http://localhost:3002

# Media Processing
IMAGE_PROCESSING_ENABLED=true
VIDEO_PROCESSING_ENABLED=false
FACE_DETECTION_ENABLED=false
OBJECT_DETECTION_ENABLED=false
TEXT_EXTRACTION_ENABLED=true
CONTENT_MODERATION_ENABLED=true

# AI Content Generation
AI_CONTENT_GENERATION_ENABLED=true
AI_DEFAULT_MODEL=gpt-3.5-turbo
AI_DEFAULT_TEMPERATURE=0.7
AI_DEFAULT_MAX_TOKENS=500
AI_COST_TRACKING_ENABLED=true

# Content Moderation
CONTENT_MODERATION_STRICT=true
AUTO_APPROVE_CONTENT=false
REQUIRE_MANUAL_APPROVAL=true
SPAM_DETECTION_ENABLED=true
PROFANITY_FILTER_ENABLED=true

# Template Management
MAX_TEMPLATES_PER_USER=100
TEMPLATE_VERSIONING_ENABLED=true
PUBLIC_TEMPLATES_ENABLED=true
TEMPLATE_SHARING_ENABLED=true

# Performance Settings
BATCH_SIZE=50
PARALLEL_PROCESSING_LIMIT=5
CACHE_TTL=3600
MEDIA_CACHE_TTL=86400

# Security Settings
UPLOAD_VIRUS_SCANNING=false
CONTENT_ENCRYPTION=false
WATERMARK_ENABLED=false
DRM_PROTECTION=false

# Analytics Settings
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=90
REAL_TIME_ANALYTICS=true
CONTENT_INSIGHTS_ENABLED=true

# Notification Settings
WEBHOOK_TIMEOUT=10000
EMAIL_NOTIFICATIONS_ENABLED=false
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=

# Backup and Archival
AUTO_BACKUP_ENABLED=false
BACKUP_INTERVAL_HOURS=24
ARCHIVE_OLD_CONTENT=false
ARCHIVE_AFTER_DAYS=365
