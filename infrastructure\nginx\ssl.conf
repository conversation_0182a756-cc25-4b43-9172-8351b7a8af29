# SSL Configuration for Enterprise Security
# Modern SSL/TLS configuration with strong security

# SSL protocols and ciphers
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;

# SSL session settings
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_session_tickets off;

# OCSP stapling
ssl_stapling on;
ssl_stapling_verify on;
ssl_trusted_certificate /etc/letsencrypt/live/${WEBH<PERSON><PERSON>_DOMAIN}/chain.pem;

# DNS resolver for OCSP
resolver ******* ******* valid=300s;
resolver_timeout 5s;

# DH parameters for perfect forward secrecy
ssl_dhparam /etc/ssl/certs/dhparam.pem;

# SSL buffer size optimization
ssl_buffer_size 4k;
