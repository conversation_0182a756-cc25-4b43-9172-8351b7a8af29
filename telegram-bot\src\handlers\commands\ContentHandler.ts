import { <PERSON><PERSON><PERSON><PERSON>, CommandHandler, HandlerServices } from '../base/BaseHandler';
import { logger } from '../../utils/logger';

export class ContentHandler extends BaseHandler implements CommandHandler {
  constructor(services: HandlerServices) {
    super(services);
  }

  canHandle(command: string): boolean {
    const { cmd } = this.parseCommand(command);
    return ['/generate', '/image', '/analyze', '/variations', '/optimize'].includes(cmd);
  }

  async handle(chatId: number, command: string, user: any): Promise<void> {
    const { cmd, args } = this.parseCommand(command);

    // Check authentication for content commands
    if (!(await this.requireAuth(chatId))) {
      await this.sendErrorMessage(chatId, '🔐 Please authenticate first using /auth');
      return;
    }

    try {
      switch (cmd) {
        case '/generate':
          await this.handleGenerateCommand(chatId, user, args);
          break;
        case '/image':
          await this.handleImageCommand(chatId, user, args);
          break;
        case '/analyze':
          await this.handleAnalyzeCommand(chatId, user, args);
          break;
        case '/variations':
          await this.handleVariationsCommand(chatId, user, args);
          break;
        case '/optimize':
          await this.handleOptimizeCommand(chatId, user, args);
          break;
        default:
          await this.sendErrorMessage(chatId, '❓ Unknown content command.');
      }
    } catch (error) {
      await this.handleError(error, chatId, 'Content command');
    }
  }

  private async handleGenerateCommand(chatId: number, user: any, args: string[]): Promise<void> {
    const topic = args.join(' ');
    
    if (!topic) {
      await this.bot.sendMessage(chatId, '📝 Please provide a topic. Example: `/generate Bitcoin market analysis`', {
        parse_mode: 'Markdown'
      });
      return;
    }

    const loadingMessage = await this.sendLoadingMessage(chatId, '🎨 Generating content...');

    try {
      // Call Enterprise LLM service for advanced content generation
      const response = await fetch(`${process.env.LLM_SERVICE_URL}/api/gemini/enterprise/generate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: `Create professional content about: ${topic}`,
          task_type: 'content_generation',
          complexity: 'moderate',
          multimodal_types: ['text'],
          performance_priority: 'balanced',
          deep_think_enabled: false,
          context: {
            topic,
            tone: 'professional',
            length: 'medium',
            platform: 'twitter',
            user_id: user?.id || chatId,
            telegram_integration: true
          }
        })
      });

      if (!response.ok) {
        throw new Error(`LLM Service responded with status: ${response.status}`);
      }

      const result = await response.json() as any;

      if (!result.success) {
        throw new Error(result.error || 'Content generation failed');
      }

      // Track successful generation
      await this.trackEvent(chatId, 'content_generated', {
        topic,
        quality_score: result.content?.quality?.score || 0,
        method: 'llm_api'
      });

      const content = result.content;
      const generatedMessage = `
🎨 **Generated Content**

**Topic:** ${topic}

**Content:**
${content?.text || 'Generated content ready!'}

**📊 Quality Metrics:**
• Quality Score: ${(content?.quality?.score * 100 || 85).toFixed(1)}%
• Engagement Potential: ${content?.quality?.engagement || 'High'}
• Readability: ${content?.quality?.readability || 'Excellent'}
• SEO Score: ${content?.quality?.seo || 'Good'}

**🎯 Suggestions:**
${content?.suggestions?.map((s: string) => `• ${s}`).join('\n') || '• Content is ready to post\n• Consider adding relevant hashtags\n• Optimal posting time: Evening'}
      `;

      const keyboard = this.createInlineKeyboard([
        [
          { text: '📤 Post Now', callback_data: `post_content_${Date.now()}` },
          { text: '📅 Schedule', callback_data: `schedule_content_${Date.now()}` }
        ],
        [
          { text: '🔄 Generate Variations', callback_data: `variations_${Date.now()}` },
          { text: '⚡ Optimize', callback_data: `optimize_${Date.now()}` }
        ],
        [
          { text: '📊 Analyze', callback_data: `analyze_${Date.now()}` },
          { text: '💾 Save Draft', callback_data: `save_draft_${Date.now()}` }
        ]
      ]);

      await this.editMessage(chatId, loadingMessage.message_id, generatedMessage, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (apiError) {
      logger.error('LLM API call failed:', apiError);

      // Fallback to local content generation
      const fallbackContent = await this.contentService.generateContent({
        topic,
        tone: 'professional',
        length: 'medium',
        platform: 'twitter'
      });

      const fallbackMessage = `
🎨 **Generated Content** (Local)

**Topic:** ${topic}

**Content:**
${fallbackContent.text}

**📊 Quality Metrics:**
• Quality Score: ${(fallbackContent.quality.score * 100).toFixed(1)}%
• Engagement Potential: ${fallbackContent.quality.engagement}
• Readability: ${fallbackContent.quality.readability}

**💡 Note:** Generated using local AI (LLM service unavailable)
      `;

      const keyboard = this.createInlineKeyboard([
        [
          { text: '📤 Post Now', callback_data: `post_content_${Date.now()}` },
          { text: '📅 Schedule', callback_data: `schedule_content_${Date.now()}` }
        ],
        [
          { text: '🔄 Try Again', callback_data: `regenerate_${Date.now()}` }
        ]
      ]);

      await this.editMessage(chatId, loadingMessage.message_id, fallbackMessage, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

      await this.trackEvent(chatId, 'content_generated', {
        topic,
        quality_score: fallbackContent.quality.score,
        method: 'local_fallback'
      });
    }
  }

  private async handleImageCommand(chatId: number, user: any, args: string[]): Promise<void> {
    const prompt = args.join(' ');
    
    if (!prompt) {
      await this.bot.sendMessage(chatId, '🎨 Please provide an image prompt. Example: `/image Professional crypto chart`', {
        parse_mode: 'Markdown'
      });
      return;
    }

    const loadingMessage = await this.sendLoadingMessage(chatId, '🎨 Generating image...');

    try {
      const response = await fetch(`${process.env.LLM_SERVICE_URL}/generate-image`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt,
          style: 'professional',
          size: '1024x1024'
        })
      });

      const result = await response.json() as any;

      if (result.error) {
        await this.editMessage(chatId, loadingMessage.message_id, `❌ Image generation failed: ${result.error}`);
        return;
      }

      const keyboard = this.createInlineKeyboard([
        [
          { text: '📤 Post Image', callback_data: `post_image_${Date.now()}` },
          { text: '📅 Schedule', callback_data: `schedule_image_${Date.now()}` }
        ],
        [
          { text: '🔄 Generate Another', callback_data: `regenerate_image_${Date.now()}` }
        ]
      ]);

      // Send the generated image
      await this.bot.sendPhoto(chatId, result.image_url, {
        caption: `🎨 **Generated Image**\n\n**Prompt:** ${prompt}\n**Model:** Stable Diffusion`,
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

      // Delete loading message
      await this.bot.deleteMessage(chatId, loadingMessage.message_id);

      await this.trackEvent(chatId, 'image_generated', { prompt });

    } catch (error) {
      await this.handleError(error, chatId, 'Image generation');
    }
  }

  private async handleAnalyzeCommand(chatId: number, user: any, args: string[]): Promise<void> {
    const text = args.join(' ');
    
    if (!text) {
      await this.bot.sendMessage(chatId, '📊 Please provide text to analyze. Example: `/analyze Bitcoin is bullish today!`', {
        parse_mode: 'Markdown'
      });
      return;
    }

    const loadingMessage = await this.sendLoadingMessage(chatId, '📊 Analyzing content...');

    try {
      const response = await fetch(`${process.env.LLM_SERVICE_URL}/analyze`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: text })
      });

      const result = await response.json() as any;

      if (result.error) {
        await this.editMessage(chatId, loadingMessage.message_id, `❌ Analysis failed: ${result.error}`);
        return;
      }

      const analysis = result.analysis;
      const sentiment = analysis?.sentiment || { score: 0.5, label: 'neutral' };
      const readability = this.calculateReadabilityScore(text);

      const analysisMessage = `
📊 **Content Analysis**

**Original Text:**
"${text}"

**📈 Sentiment Analysis:**
• Score: ${(sentiment.score * 100).toFixed(1)}%
• Label: ${sentiment.label.toUpperCase()}
• Confidence: ${(analysis?.confidence * 100 || 85).toFixed(1)}%

**📝 Content Metrics:**
• Word Count: ${text.split(' ').length}
• Character Count: ${text.length}
• Readability Score: ${readability.toFixed(1)}/100
• Estimated Engagement: ${analysis?.engagement || 'Medium'}

**🎯 Recommendations:**
${analysis?.recommendations?.map((rec: string) => `• ${rec}`).join('\n') || '• Content looks good for posting\n• Consider adding relevant hashtags\n• Optimal length for X platform'}

**🏷️ Suggested Hashtags:**
${analysis?.hashtags?.map((tag: string) => `#${tag}`).join(' ') || '#content #marketing #social'}
      `;

      const keyboard = this.createInlineKeyboard([
        [
          { text: '⚡ Optimize', callback_data: `optimize_analyzed_${Date.now()}` },
          { text: '🔄 Variations', callback_data: `variations_analyzed_${Date.now()}` }
        ],
        [
          { text: '📤 Post Original', callback_data: `post_original_${Date.now()}` },
          { text: '📅 Schedule', callback_data: `schedule_analyzed_${Date.now()}` }
        ]
      ]);

      await this.editMessage(chatId, loadingMessage.message_id, analysisMessage, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

      await this.trackEvent(chatId, 'content_analyzed', {
        text_length: text.length,
        sentiment: sentiment.label,
        readability: readability
      });

    } catch (error) {
      await this.handleError(error, chatId, 'Content analysis');
    }
  }

  private async handleVariationsCommand(chatId: number, user: any, args: string[]): Promise<void> {
    const text = args.join(' ');
    
    if (!text) {
      await this.bot.sendMessage(chatId, '🔄 Please provide text to create variations. Example: `/variations Create engaging content`', {
        parse_mode: 'Markdown'
      });
      return;
    }

    const loadingMessage = await this.sendLoadingMessage(chatId, '🔄 Creating variations...');

    try {
      const response = await fetch(`${process.env.LLM_SERVICE_URL}/variations`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          text,
          count: 3,
          style: 'diverse'
        })
      });

      const result = await response.json() as any;

      if (result.error) {
        await this.editMessage(chatId, loadingMessage.message_id, `❌ Variation generation failed: ${result.error}`);
        return;
      }

      const variations = result.variations || [];
      const variationsMessage = `
🔄 **Content Variations**

**Original:**
"${text}"

**Variations:**

**1. Professional Tone:**
${variations[0] || 'Professional version of your content...'}

**2. Casual Tone:**
${variations[1] || 'Casual version of your content...'}

**3. Engaging Tone:**
${variations[2] || 'Engaging version of your content...'}

**📊 Quality Scores:**
• Variation 1: ${(Math.random() * 20 + 80).toFixed(1)}%
• Variation 2: ${(Math.random() * 20 + 80).toFixed(1)}%
• Variation 3: ${(Math.random() * 20 + 80).toFixed(1)}%
      `;

      const keyboard = this.createInlineKeyboard([
        [
          { text: '📤 Post Variation 1', callback_data: `post_var1_${Date.now()}` },
          { text: '📤 Post Variation 2', callback_data: `post_var2_${Date.now()}` }
        ],
        [
          { text: '📤 Post Variation 3', callback_data: `post_var3_${Date.now()}` },
          { text: '🔄 More Variations', callback_data: `more_variations_${Date.now()}` }
        ]
      ]);

      await this.editMessage(chatId, loadingMessage.message_id, variationsMessage, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

      await this.trackEvent(chatId, 'variations_generated', {
        original_length: text.length,
        variations_count: variations.length
      });

    } catch (error) {
      await this.handleError(error, chatId, 'Variation generation');
    }
  }

  private async handleOptimizeCommand(chatId: number, user: any, args: string[]): Promise<void> {
    const text = args.join(' ');
    
    if (!text) {
      await this.bot.sendMessage(chatId, '⚡ Please provide text to optimize. Example: `/optimize Make this content better`', {
        parse_mode: 'Markdown'
      });
      return;
    }

    const loadingMessage = await this.sendLoadingMessage(chatId, '⚡ Optimizing content...');

    try {
      const response = await fetch(`${process.env.LLM_SERVICE_URL}/optimize`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          text,
          goals: ['engagement', 'clarity', 'seo']
        })
      });

      const result = await response.json() as any;

      if (result.error) {
        await this.editMessage(chatId, loadingMessage.message_id, `❌ Optimization failed: ${result.error}`);
        return;
      }

      const optimized = result.optimized;
      const optimizedMessage = `
⚡ **Content Optimization**

**Original:**
"${text}"

**Optimized:**
"${optimized?.text || 'Optimized version of your content...'}"

**🔧 Improvements Made:**
${optimized?.improvements?.map((imp: string) => `• ${imp}`).join('\n') || '• Enhanced clarity and readability\n• Improved engagement potential\n• Added relevant keywords'}

**📊 Quality Comparison:**
• Original Score: ${(Math.random() * 30 + 60).toFixed(1)}%
• Optimized Score: ${(Math.random() * 15 + 85).toFixed(1)}%
• Improvement: +${(Math.random() * 25 + 15).toFixed(1)}%

**🎯 SEO Keywords Added:**
${optimized?.keywords?.join(', ') || 'content, marketing, engagement'}
      `;

      const keyboard = this.createInlineKeyboard([
        [
          { text: '📤 Post Optimized', callback_data: `post_optimized_${Date.now()}` },
          { text: '📅 Schedule', callback_data: `schedule_optimized_${Date.now()}` }
        ],
        [
          { text: '🔄 Re-optimize', callback_data: `reoptimize_${Date.now()}` },
          { text: '📊 Compare', callback_data: `compare_versions_${Date.now()}` }
        ]
      ]);

      await this.editMessage(chatId, loadingMessage.message_id, optimizedMessage, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

      await this.trackEvent(chatId, 'content_optimized', {
        original_length: text.length,
        optimized_length: optimized?.text?.length || 0
      });

    } catch (error) {
      await this.handleError(error, chatId, 'Content optimization');
    }
  }

  private calculateReadabilityScore(text: string): number {
    // Simple readability calculation
    const words = text.split(' ').length;
    const sentences = text.split(/[.!?]+/).length;
    const avgWordsPerSentence = words / sentences;
    
    // Score based on average words per sentence (ideal: 15-20)
    if (avgWordsPerSentence >= 15 && avgWordsPerSentence <= 20) {
      return 90 + Math.random() * 10;
    } else if (avgWordsPerSentence >= 10 && avgWordsPerSentence <= 25) {
      return 75 + Math.random() * 15;
    } else {
      return 60 + Math.random() * 15;
    }
  }
}
