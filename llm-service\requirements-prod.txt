# Production Requirements for LLM Service
# Comprehensive dependencies for enterprise deployment

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP Client
aiohttp==3.9.1
httpx==0.25.2
requests==2.31.0

# Database
asyncpg==0.29.0
psycopg2-binary==2.9.9
redis==5.0.1
sqlalchemy==2.0.23
alembic==1.13.1

# Caching
aiocache==0.12.2

# Monitoring and Logging
prometheus-client==0.19.0
structlog==23.2.0
python-json-logger==2.0.7
sentry-sdk==1.38.0

# OpenTelemetry for distributed tracing
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-requests==0.42b0
opentelemetry-instrumentation-redis==0.42b0
opentelemetry-exporter-jaeger==1.21.0
opentelemetry-semantic-conventions==0.42b0

# Event streaming with Kafka
kafka-python==2.0.2
confluent-kafka==2.3.0

# Service discovery
python-consul==1.1.0

# Circuit breaker pattern
pybreaker==1.0.2

# Security
cryptography==43.0.3
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Validation and Serialization
marshmallow==3.20.1
cerberus==1.3.5

# Rate Limiting
slowapi==0.1.9
limits==3.6.0

# Background Tasks
celery==5.3.4
kombu==5.3.4

# Utilities
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7
rich==13.7.0

# Development and Testing (for production debugging)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# Performance
orjson==3.9.10
ujson==5.8.0

# Networking
dnspython==2.4.2

# File handling
aiofiles==23.2.1

# Metrics and Profiling
py-spy==0.3.14
memory-profiler==0.61.0

# Production WSGI/ASGI
gunicorn==21.2.0

# Health checks
healthcheck==1.3.3

# Environment management
environs==10.0.0

# Async utilities
asyncio-throttle==1.0.2
aioredis==2.0.1

# JSON handling
jsonschema==4.20.0

# HTTP middleware
starlette==0.27.0

# Concurrent processing (built into Python 3.2+)
# concurrent-futures==3.1.1  # Not needed for Python 3.11

# System monitoring
psutil==5.9.6

# Timezone handling
zoneinfo==0.2.1; python_version < "3.9"

# Production optimizations
cython==3.0.6
