{"name": "x-marketing-telegram-bot", "version": "1.0.0", "description": "Telegram bot interface for X Marketing Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@opentelemetry/api": "^1.7.0", "@opentelemetry/auto-instrumentations-node": "^0.40.3", "@opentelemetry/exporter-jaeger": "^1.18.1", "@opentelemetry/resources": "^1.18.1", "@opentelemetry/sdk-node": "^0.45.1", "@opentelemetry/sdk-trace-base": "^1.18.1", "@opentelemetry/semantic-conventions": "^1.18.1", "@prisma/client": "^6.11.1", "@types/pg": "^8.15.4", "@types/uuid": "^9.0.7", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "chart.js": "^4.4.0", "consul": "^0.40.0", "cors": "^2.8.5", "cron": "^3.1.6", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dotenv": "^16.6.1", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.2.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "moment": "^2.29.4", "multer": "^2.0.0", "node-cron": "^3.0.3", "node-telegram-bot-api": "^0.64.0", "pg": "^8.16.3", "prom-client": "^15.1.0", "qrcode": "^1.5.3", "redis": "^4.6.10", "uuid": "^9.0.1", "validator": "^13.11.0", "winston": "^3.17.0", "ws": "^8.18.3"}, "devDependencies": {"@types/consul": "^0.40.2", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jest": "^29.5.8", "@types/lodash": "^4.14.202", "@types/node": "^20.10.0", "@types/node-telegram-bot-api": "^0.64.9", "@types/qrcode": "^1.5.5", "@types/uuid": "^9.0.7", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "telegraf": "^4.16.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"]}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "types": "./dist/index.d.ts"}