# Post Management Service Environment Template
# Copy this file to .env.local and fill in the actual values

# Application Configuration
NODE_ENV=development
PORT=3015
HOST=localhost

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/x_marketing_platform
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_KEY_PREFIX=post-mgmt:

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=post-management-service
KAFKA_GROUP_ID=post-management-group
DISABLE_KAFKA=false

# Consul Configuration
CONSUL_HOST=localhost
CONSUL_PORT=8500
CONSUL_SERVICE_NAME=post-management-service
DISABLE_CONSUL=false

# Twitter API Configuration (Regional restrictions apply)
TWITTER_CONSUMER_KEY=not-available-regional-restrictions
TWITTER_CONSUMER_SECRET=not-available-regional-restrictions
TWITTER_BEARER_TOKEN=not-available-regional-restrictions
TWITTER_API_VERSION=v2
TWITTER_RATE_LIMIT_BUFFER=0.8

# JWT Configuration (REQUIRED - Generate secure keys)
JWT_SECRET=your-super-secret-jwt-key-that-should-be-at-least-32-characters-long
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key-that-should-be-at-least-32-characters-long

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
POST_RATE_LIMIT_MAX=50

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9095
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Service Discovery
SERVICE_VERSION=1.0.0
SERVICE_TAGS=post-management,scheduling,publishing,analytics,microservice

# Health Checks
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Enterprise Features
ENTERPRISE_MODE=true
ENABLE_AUDIT_LOGGING=true
ENABLE_SECURITY_MONITORING=true

# Post Management Specific
MAX_POSTS_PER_USER=10000
MAX_SCHEDULED_POSTS=1000
POST_QUEUE_BATCH_SIZE=10
POST_PROCESSING_TIMEOUT=30000
SCHEDULER_INTERVAL=60000

# External Services (Backend Integration)
BACKEND_SERVICE_URL=http://localhost:3001
USER_MANAGEMENT_SERVICE_URL=http://localhost:3011
ACCOUNT_MANAGEMENT_SERVICE_URL=http://localhost:3012
CAMPAIGN_MANAGEMENT_SERVICE_URL=http://localhost:3013
CONTENT_MANAGEMENT_SERVICE_URL=http://localhost:3014
TELEGRAM_BOT_URL=http://localhost:3002

# Scheduling Settings
ENABLE_OPTIMAL_TIMING=true
OPTIMAL_TIMING_ANALYSIS_INTERVAL=********
MIN_POST_INTERVAL=300000
MAX_POSTS_PER_HOUR=12
MAX_POSTS_PER_DAY=100

# Publishing Settings
ENABLE_AUTO_RETRY=true
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_BASE=60000
RETRY_DELAY_MULTIPLIER=2

# Analytics Settings
ENABLE_REAL_TIME_ANALYTICS=true
ANALYTICS_UPDATE_INTERVAL=300000
METRICS_RETENTION_DAYS=90
ENABLE_PERFORMANCE_TRACKING=true

# Queue Management
QUEUE_PROCESSING_INTERVAL=30000
QUEUE_MAX_SIZE=10000
QUEUE_PRIORITY_LEVELS=4
DEAD_LETTER_QUEUE_ENABLED=true

# Performance Settings
BATCH_SIZE=50
PARALLEL_PROCESSING_LIMIT=5
CACHE_TTL=3600
METRICS_CACHE_TTL=300

# Security Settings
ENABLE_CONTENT_VALIDATION=true
ENABLE_SPAM_DETECTION=true
ENABLE_RATE_LIMIT_PROTECTION=true
ENABLE_ACCOUNT_SAFETY_CHECKS=true

# Notification Settings
WEBHOOK_TIMEOUT=10000
EMAIL_NOTIFICATIONS_ENABLED=false
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=

# Backup and Recovery
ENABLE_QUEUE_PERSISTENCE=true
BACKUP_INTERVAL_HOURS=6
ENABLE_DISASTER_RECOVERY=true

# Feature Flags
ENABLE_THREAD_POSTING=true
ENABLE_MEDIA_POSTING=true
ENABLE_POLL_POSTING=true
ENABLE_QUOTE_TWEETS=true
ENABLE_REPLY_POSTING=true

# Frontend Integration
FRONTEND_URL=http://localhost:3000

# Development Settings
ENABLE_DEBUG_LOGGING=false
ENABLE_MOCK_TWITTER_API=true
SIMULATE_PUBLISHING_DELAYS=true
MOCK_ANALYTICS_DATA=true

# Testing Settings
TEST_DATABASE_URL=postgresql://postgres:password@localhost:5432/x_marketing_platform_test
ENABLE_TEST_ENDPOINTS=false
TEST_USER_ID=test-user-123
TEST_ACCOUNT_ID=test-account-456

# Compliance and Legal
GDPR_COMPLIANCE_ENABLED=true
DATA_RETENTION_DAYS=365
ENABLE_DATA_ANONYMIZATION=true
PRIVACY_MODE=false

# Internationalization
DEFAULT_TIMEZONE=UTC
SUPPORTED_TIMEZONES=UTC,America/New_York,Europe/London,Asia/Tokyo
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de,ja

# Advanced Features
ENABLE_AI_OPTIMIZATION=false
ENABLE_SENTIMENT_ANALYSIS=false
ENABLE_TREND_DETECTION=false
ENABLE_COMPETITOR_ANALYSIS=false

# Infrastructure
ENABLE_HORIZONTAL_SCALING=true
ENABLE_AUTO_SCALING=false
MIN_INSTANCES=1
MAX_INSTANCES=10
SCALE_UP_THRESHOLD=80
SCALE_DOWN_THRESHOLD=20

# Observability
ENABLE_DISTRIBUTED_TRACING=true
TRACE_SAMPLING_RATE=0.1
ENABLE_CUSTOM_METRICS=true
METRICS_NAMESPACE=post_management

# Error Handling
ENABLE_ERROR_REPORTING=true
ERROR_REPORTING_SERVICE=sentry
SENTRY_DSN=
ENABLE_CIRCUIT_BREAKER=true
CIRCUIT_BREAKER_THRESHOLD=5

# Content Safety
ENABLE_CONTENT_MODERATION=true
CONTENT_MODERATION_STRICT=false
ENABLE_PROFANITY_FILTER=true
ENABLE_SPAM_DETECTION=true
ENABLE_HATE_SPEECH_DETECTION=false

# Business Intelligence
ENABLE_BUSINESS_ANALYTICS=true
ANALYTICS_WAREHOUSE_URL=
ENABLE_DATA_EXPORT=true
EXPORT_FORMAT=json,csv,parquet

# API Configuration
API_VERSION=v1
ENABLE_API_VERSIONING=true
ENABLE_API_DOCUMENTATION=true
SWAGGER_ENABLED=true

# Caching Strategy
ENABLE_REDIS_CLUSTERING=false
REDIS_CLUSTER_NODES=
CACHE_STRATEGY=lru
CACHE_MAX_MEMORY=512mb

# Message Queue
ENABLE_MESSAGE_QUEUE=true
MESSAGE_QUEUE_TYPE=kafka
QUEUE_RETENTION_HOURS=168
ENABLE_DEAD_LETTER_QUEUE=true

# Backup Strategy
BACKUP_STRATEGY=incremental
BACKUP_STORAGE=s3
BACKUP_ENCRYPTION=true
BACKUP_COMPRESSION=true

# Disaster Recovery
DR_ENABLED=false
DR_REGION=us-west-2
DR_RTO_MINUTES=60
DR_RPO_MINUTES=15

# Load Balancing
ENABLE_LOAD_BALANCING=true
LOAD_BALANCER_TYPE=round_robin
HEALTH_CHECK_PATH=/health
HEALTH_CHECK_INTERVAL=30

# SSL/TLS Configuration
ENABLE_HTTPS=false
SSL_CERT_PATH=
SSL_KEY_PATH=
TLS_VERSION=1.3

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
CORS_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Correlation-ID

# WebSocket Configuration
ENABLE_WEBSOCKETS=false
WEBSOCKET_PORT=3016
WEBSOCKET_PATH=/ws

# GraphQL Configuration
ENABLE_GRAPHQL=false
GRAPHQL_ENDPOINT=/graphql
GRAPHQL_PLAYGROUND=true

# Microservice Communication
ENABLE_SERVICE_MESH=false
SERVICE_MESH_TYPE=istio
ENABLE_CIRCUIT_BREAKER=true
TIMEOUT_SECONDS=30
